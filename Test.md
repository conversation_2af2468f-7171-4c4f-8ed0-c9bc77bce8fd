
手动配置路由表的方法
ifconfig utun5 ******** ******** up

### 删除默认路由
删除异常的分流路由
route delete -net 0/1
route delete -net 128.0/1

删除现有默认路由
route delete default
### 全部流量转发
route add default -interface utun5 

备用 添加utun5为默认路由
route add -net 0.0.0.0 -interface utun5
验证
netstat -rn | grep default

### 只转发特定的流量
route add -net *******/32 -interface utun5


### 架构说明

Swift/C 层代码 ： 调用 iOS 系统调用，实现 
- VPN 的创建 
- utun的配置（ifconfig的功能）
- 系统级路由表的管理: IP级别白名单，备份和恢复，接口的管理
- 流量转发
- 程序的入口，VPN 和 代理的启停，远程控制

Rust
- 代理： 协议支持
- 透明代理

Golang
- 抓包代理：可配置的流量转发、TLS证书、TLS流量的加解密


同时转发所有 IPv4 和 IPv6 流量
🛠️ 完整配置步骤（使用 ifconfig + route）
1. 配置 utun 接口双栈地址
bash
# 配置 IPv4 点对点地址
ifconfig utun5 inet ******** ******** up

# 配置 IPv6 点对点地址
ifconfig utun5 inet6 fe80::1%utun5 2001:db8::1 2001:db8::2 prefixlen 128 up
参数解析：

fe80::1%utun5：本地链路地址（必需）

2001:db8::1：本地全局地址

2001:db8::2：对端全局地址

2. 设置双栈默认路由
bash
# 删除现有默认路由
route delete default
route delete -inet6 default

# 添加 IPv4 默认路由
route add -net 0.0.0.0 -interface utun5

# 添加 IPv6 默认路由
route add -inet6 ::/0 -interface utun5
3. 验证接口状态
bash
ifconfig utun5 | grep -E 'inet|inet6'
期望输出：

text
inet ******** --> ******** netmask 0xff000000 
inet6 fe80::1%utun5 prefixlen 64 scopeid 0x5 
inet6 2001:db8::1 --> 2001:db8::2 prefixlen 128 
4. 验证路由表
bash
netstat -rn | grep -E 'default|::/0'
期望输出：

text
default            utun5              UScg            utun5   # IPv4
::/0               utun5              UGcIg           utun5   # IPv6
🌐 流量转发验证测试
测试 1：IPv4 连通性
bash
ping -c 4 ******* -I utun5
traceroute -i utun5 www.apple.com
预期结果：

成功 ping 通

路径显示 utun5 接口

测试 2：IPv6 连通性
bash
ping6 -c 4 2001:4860:4860::8888 -I utun5
traceroute6 -i utun6 ipv6.google.com
预期结果：

成功 ping 通 IPv6 DNS

路径经过 utun5

测试 3：双栈网站访问
bash
curl -4 https://ifconfig.co  # 应返回VPN IPv4
curl -6 https://ifconfig.co  # 应返回VPN IPv6
⚠️ 关键注意事项
1. VPN 服务端要求
协议	服务端必备能力	推荐工具
IPv4	NAT 转发	iptables
IPv6	NDP 代理/路由通告	radvd/dhcpv6-server
双栈	同步处理 v4/v6 数据包	WireGuard/OpenVPN 双栈版
2. iOS 特殊限制
bash
# 需启用 IPv6 路由转发
sysctl -w net.inet6.ip6.forwarding=1

# 防止 RS 包泄漏
ipfw add 100 deny ipv6-icmp from any to any icmp6types 133 via ! utun5
3. 防火墙配置
bash
# 允许 VPN 流量
ipfw add 200 allow ip from any to any via utun5

# 屏蔽非 VPN 出口流量
ipfw add 300 deny ip from any to any not via utun5
ipfw add 400 deny ip6 from any to any not via utun5
🔧 故障排查指南
问题 1：IPv6 流量不走 utun5
解决方案：

bash
# 禁用隐私扩展
sysctl -w net.inet6.ip6.use_tempaddr=0

# 清除 IPv6 缓存
ndc interface clearaddrs utun5
问题 2：DNS 泄漏
解决方案：

bash
# 强制使用 IPv4/IPv6 DNS
networksetup -setdnsservers "Celluar" 2001:4860:4860::8888
networksetup -setdnsservers "Wi-Fi" 2001:4860:4860::8888

# 屏蔽本地 DNS
ipfw add 500 deny udp from any to any 53 via ! utun5
📊 性能优化建议
MTU 调整：

bash
ifconfig utun5 mtu 1400  # 兼容 PPPoE/IPv6 头部开销
QoS 标记：

bash
# 标记 VPN 流量
ipfw add 600 skipto 2000 ip from any to any tagged 0x100 via utun5
多路径路由：

bash
# 分流大流量下载
route add -net 0/1 -interface en0
route add -net 128/1 -interface utun5


iPhone:~ root# ifconfig utun5 inet6 fe80::1%utun5 up
iPhone:~ root# ifconfig utun5 inet ******** ******** up
iPhone:~ root# route delete default
delete net default
iPhone:~ root# route delete -inet6 default
delete net default
iPhone:~ root# route add -net 0.0.0.0 -gateway ********
add net 0.0.0.0
iPhone:~ root# route add -inet6 ::/0 -gateway fe80::1%utun5
add net ::/0
iPhone:~ root# 