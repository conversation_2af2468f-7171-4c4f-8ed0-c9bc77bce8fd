INSTALL_TARGET_PROCESSES = iAntsUT

INCLUDE_PATH = $(THEOS_PROJECT_DIR)/include
COMMON_INCLUDE_PATH = $(THEOS_PROJECT_DIR)/Sources/Base/include
FRAMEWORK_PATH = $(THEOS_PROJECT_DIR)/frameworks


UPLOAD ?= 0
DEBUG ?= 0
MITM ?= 0

THEOS_DEVICE_IP=127.0.0.1
THEOS_DEVICE_PORT=2222
FINALPACKAGE=1

SCHEME ?= roothide

THEOS_PACKAGE_SCHEME=${SCHEME}

ifeq ($(THEOS_PACKAGE_SCHEME), rootless)
    # ARCHS :=  arm64 arm64e
	ARCHS := arm64
    TARGET := iphone:clang:latest:15.0 ##最低可能到14
	ROOT_SCHEME := -DROOTLESS
	PROG_PATH := /var/jb/usr/sbin/iAntsUT
else ifeq ($(THEOS_PACKAGE_SCHEME), roothide)
    ARCHS := arm64
    TARGET := iphone:clang:latest:15.0 ##最低可能到14
	ROOT_SCHEME := -DROOTHIDE
	PROG_PATH := /usr/sbin/iAntsUT
else
    # ARCHS := arm64 arm64e
	ARCHS := arm64
    TARGET := iphone:clang:latest:12.0
	ROOT_SCHEME := -DROOTFULL
	PROG_PATH := /usr/sbin/iAntsUT
endif

PLIST_PATH := layout/Library/LaunchDaemons/com.iants.ut.plist
BUILD_DATE = $(shell date +"%s")

include $(THEOS)/makefiles/common.mk

TOOL_NAME = iAntsUT
$(TOOL_NAME)_FILES = $(shell find Sources -name "*.m") $(shell find Sources -name "*.mm") $(shell find Sources -name "*.swift")

$(TOOL_NAME)_SWIFTFLAGS = -import-objc-header Sources/Bridging-Header.h
# $(TOOL_NAME)_SWIFTFLAGS += -parse-as-library
$(TOOL_NAME)_SWIFTFLAGS += -DTHEOS
$(TOOL_NAME)_SWIFTFLAGS += $(ROOT_SCHEME)
$(TOOL_NAME)_SWIFTFLAGS += -F$(FRAMEWORK_PATH)
$(TOOL_NAME)_SWIFTFLAGS += -Xcc -I$(INCLUDE_PATH) -Xcc -I$(COMMON_INCLUDE_PATH)

$(TOOL_NAME)_CFLAGS = -fobjc-arc 
$(TOOL_NAME)_CFLAGS += -I$(COMMON_INCLUDE_PATH)
$(TOOL_NAME)_CFLAGS += -I$(INCLUDE_PATH) 
$(TOOL_NAME)_CFLAGS += $(ROOT_SCHEME)
# $(TOOL_NAME)_CFLAGS += -fno-modules
# $(TOOL_NAME)_CFLAGS += -I/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/System/Library/Frameworks/Kernel.framework/Headers
$(TOOL_NAME)_LDFLAGS = -L$(THEOS_PROJECT_DIR)/libs -ltun2proxy
$(TOOL_NAME)_LDFLAGS += -F$(FRAMEWORK_PATH) -framework SwiftProtobuf
$(TOOL_NAME)_LDFLAGS += -framework IOKit


$(TOOL_NAME)_CODESIGN_FLAGS = -Sentitlements.plist
$(TOOL_NAME)_INSTALL_PATH = /usr/sbin

## debug
ifeq ($(DEBUG), 1)
    $(TOOL_NAME)_CFLAGS += -g -O0 -DDEBUG
    $(TOOL_NAME)_LDFLAGS += -g
    $(TOOL_NAME)_SWIFTFLAGS += -DDEBUG
endif

## TODO 添加tun2proxy的编译 - 动态选择是否编译
build_tun2proxy:
	cd dep/tun2proxy & cargo build --release --target aarch64-apple-ios

## TODO 动态选择是否编译 mitm 工具，尽量避免每次都编译
build_mitm: 
	cd mitm & ./build.sh ios -r -s
	cd ../

include $(THEOS_MAKE_PATH)/tool.mk

#$(info SWIFTFLAGS: $($(TOOL_NAME)_SWIFTFLAGS))
ifeq ($(DEBUG), 1)
    TARGETNAME := "com.iants.ut_${THEOS_PACKAGE_BASE_VERSION}+debug_${THEOS_PACKAGE_ARCH}.deb"
	VTAG := "debug"
else
    TARGETNAME := "com.iants.ut_${THEOS_PACKAGE_BASE_VERSION}_${THEOS_PACKAGE_ARCH}.deb"
	VTAG := "stable"
endif

VERSION = ${THEOS_PACKAGE_BASE_VERSION}-$(BUILD_DATE)-$(VTAG)

version:
	echo "struct DAEMON {" > Sources/Main/Version.swift; \
	echo "	static let versionNum = \"${THEOS_PACKAGE_BASE_VERSION}\"" >> Sources/Main/Version.swift; \
	echo "	static let buildNum = \"$(BUILD_DATE)\"" >> Sources/Main/Version.swift; \
	echo "	static let version = \"$(VERSION)\"" >> Sources/Main/Version.swift; \
	echo "	static let vtag = \"$(VTAG)\"" >> Sources/Main/Version.swift; \
	echo "}" >> Sources/Main/Version.swift;

## 缺少前置的编译


before-all::
	$(MAKE) version
	@echo ">>> SCHEME=${THEOS_PACKAGE_SCHEME}"
	@sed -i '' "s/^BuildAt: .*/BuildAt: $(BUILD_DATE)/" $(THEOS_PROJECT_DIR)/control

before-stage::
	@echo ">>> [before-all] Replacing iAntsUTPATH with $(PROG_PATH) in $(PLIST_PATH)"
	@sed -i '' 's#iAntsUTPATH#$(PROG_PATH)#g' $(PLIST_PATH)

after-package::
    ifeq ($(UPLOAD), 1)
	    @echo "upload ./packages/${TARGETNAME}"
	    curl -k "https://nlb-lrwcurgcsatuhvfiz3.cn-shanghai.nlb.aliyuncsslb.com/api/tweak?versionType=${VTAG}" \
	    -H "Authorization: Bearer data666iants." \
	    -F "file=@./packages/${TARGETNAME}"
    endif
	@echo ">>> [after-package] Restoring iAntsUTPATH placeholder in $(PLIST_PATH)"
	@sed -i '' 's#$(PROG_PATH)#iAntsUTPATH#g' $(PLIST_PATH)