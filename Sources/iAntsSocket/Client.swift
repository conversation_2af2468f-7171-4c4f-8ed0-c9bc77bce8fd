import Foundation
import SwiftProtobuf

#if !THEOS
import UtunProxy
import Common
#endif

/// RemoteClient - WebSocket 客户端单例
/// 负责连接 ws://127.0.0.1:6888/utpipe，处理消息收发和断线重连
class RemoteClient {

    // MARK: - 单例
    static let shared = RemoteClient()

    // MARK: - 属性
    private let logger = DaLog(subsystem: "com.iants.ut", category: "RemoteClient")
    private let wsURL = URL(string: "ws://127.0.0.1:6888/utpipe")!

    // WebSocket 客户端
    private var wsClient: WSClient?

    // 重连相关
    private var reconnectTask: Task<Void, Never>?
    private var heartbeatTask: Task<Void, Never>?
    private var isStarted = false

    // 重连配置
    private let maxRetryInterval: TimeInterval = 300 // 5分钟
    private let baseRetryInterval: TimeInterval = 0.5  // 0.5秒，第一次重试更快
    private var currentRetryInterval: TimeInterval = 0.5

    // MARK: - 初始化
    private init() {
        logger.info("🔌 RemoteClient 初始化完成，未连接")
    }

    deinit {
        logger.info("♻️ RemoteClient 析构")
        reconnectTask?.cancel()
        heartbeatTask?.cancel()
    }

    // MARK: - 公共接口

    /// 启动 WebSocket 连接和消息监听
    func start() async {
        guard !isStarted else {
            logger.info("⚠️ RemoteClient 已经启动，跳过重复启动")
            return
        }

        isStarted = true
        logger.info("🚀 启动 RemoteClient")

        // 启动连接任务
        startConnectionTask()
    }

    /// 停止 WebSocket 连接
    func stop() async {
        guard isStarted else { return }

        isStarted = false
        logger.info("⏹️ 停止 RemoteClient")

        // 取消所有任务
        reconnectTask?.cancel()
        heartbeatTask?.cancel()

        // 断开连接
        if let client = wsClient {
            client.disconnect()
            wsClient = nil
        }

        // 重置重连间隔
        currentRetryInterval = baseRetryInterval
    }

    // MARK: - 私有方法

    /// 启动连接任务
    private func startConnectionTask() {
        reconnectTask?.cancel()
        reconnectTask = Task { [weak self] in
            await self?.connectionLoop()
        }
    }

    /// 连接循环 - 负责连接和重连逻辑
    private func connectionLoop() async {
        while isStarted && !Task.isCancelled {
            do {
                // 创建新的 WebSocket 客户端
                let client = WSClient(serverURL: wsURL)
                client.delegate = self
                wsClient = client

                logger.info("🔄 尝试连接到 \(wsURL)")

                // 尝试连接
                try await client.connect()

                // 连接成功，等待断开
                while isStarted && !Task.isCancelled && client.state == .connected {
                    try? await Task.sleep(nanoseconds: 1_000_000_000) // 1秒检查一次
                }

            } catch {
                logger.error("❌ 连接失败: \(error)")
            }

            // 如果还需要继续运行，则等待后重连
            if isStarted && !Task.isCancelled {
                logger.info("⏳ \(currentRetryInterval)秒后重试连接")
                try? await Task.sleep(nanoseconds: UInt64(currentRetryInterval * 1_000_000_000))

                // 指数退避，最大5分钟
                currentRetryInterval = min(currentRetryInterval * 2, maxRetryInterval)
            }
        }
    }



    /// 启动心跳任务（全局唯一）
    private func startHeartbeat(client: WSClient) {
        // 如果已有心跳任务，先停止
        stopHeartbeat()

        // 使用独立的后台队列来执行心跳任务，避免被其他任务阻塞
        heartbeatTask = Task.detached { [weak self] in
            self?.logger.info("💓 心跳任务开始运行")

            while !Task.isCancelled {
                do {
                    // 发送心跳消息
                    await self?.sendHeartbeat(client: client)

                    // 等待3分钟
                    self?.logger.debug("💓 心跳任务等待3分钟...")
                    try await Task.sleep(nanoseconds: 180_000_000_000)
                    self?.logger.debug("💓 心跳任务等待结束，准备发送下一次心跳")
                } catch {
                    // 任务被取消或其他错误
                    self?.logger.info("💔 心跳任务结束: \(error)")
                    break
                }
            }

            self?.logger.info("💔 心跳任务循环结束")
        }

        logger.info("💓 启动心跳任务")
    }

    /// 停止心跳任务
    private func stopHeartbeat() {
        heartbeatTask?.cancel()
        heartbeatTask = nil
        logger.info("💔 停止心跳任务")
    }

    /// 发送心跳消息
    private func sendHeartbeat(client: WSClient) async {
        do {
            // 创建心跳消息
            var heartbeat = IAnts_Heartbeat()
            heartbeat.version = DAEMON.version

            var message = IAnts_UTMessage()
            // 正确设置消息类型和消息体
            message.messageType = .commonType(.heartbeat)
            message.body = .heartbeat(heartbeat)

            let data = try message.serializedData()
            try await client.sendProtobufData(data)

            logger.debug("💓 发送心跳")
        } catch {
            logger.error("❌ 发送心跳失败: \(error)")
        }
    }

    // MARK: - 消息处理

    /// 处理接收到的消息
    private func handleMessage(_ data: Data) async {
        do {
            // 反序列化 protobuf 消息
            let message = try IAnts_UTMessage(serializedBytes: data)
            logger.debug("📨 收到消息，数据长度: \(data.count) bytes")

            // 根据消息类型处理
            if let messageType = message.messageType {
                switch messageType {
                case .commonType(_):
                    await handleCommonMessage(message)
                case .utType(_):
                    await handleUTMessage(message)
                }
            } else {
                logger.warning("⚠️ 收到未知消息类型，完整消息: \(message)")
            }

        } catch {
            logger.error("❌ 消息反序列化失败: \(error), 数据长度: \(data.count) bytes, 数据内容: \(data.prefix(100))")
        }
    }

    /// 处理公共消息
    private func handleCommonMessage(_ message: IAnts_UTMessage) async {
        guard let messageType = message.messageType, case .commonType(let commonType) = messageType else {
            logger.warning("⚠️ 公共消息类型解析失败")
            return
        }

        switch commonType {
        case .heartbeat:
            logger.debug("💓 收到心跳响应: \(message): \(String(describing: message.body))")
            // TODO: 处理心跳响应 暂时没啥作用


        case .error:
            if let body = message.body, case .error(let errorMsg) = body {
                logger.error("❌ 收到错误消息: \(errorMsg.text), 完整消息: \(message)")
            } else {
                logger.error("❌ 收到错误消息但解析失败: \(message)")
            }

        case .UNRECOGNIZED(_):
            logger.warning("⚠️ 收到未识别的公共消息类型: \(message)：\(commonType) ")
        }
    }

    /// 处理 UT 消息
    private func handleUTMessage(_ message: IAnts_UTMessage) async {
        guard let messageType = message.messageType, case .utType(let utType) = messageType else {
            logger.warning("⚠️ UT 消息类型解析失败")
            return
        }

        switch utType {
        case .utStatus:
            logger.info("📊 收到状态请求: \(message)")
            await handleUTStatusRequest(message)

        case .utGetConfig:
            logger.info("⚙️ 收到获取配置请求: \(message)")
            await handleUTGetConfigRequest(message)

        case .utRouterConfig:
            logger.info("🌐 收到路由配置请求: \(message)")
            await handleUTRouterConfigRequest(message)

        case .utRouterAction:
            logger.info("🔄 收到路由动作请求: \(message)")
            await handleUTRouterActionRequest(message)

        case .utIpConfig:
            logger.info("🌍 收到IP配置请求: \(message)")
            await handleUTIPConfigRequest(message)

        case .utProxyConfig:
            logger.info("🔧 收到代理配置请求: \(message)")
            await handleUTProxyConfigRequest(message)

        case .utProxyAction:
            logger.info("🚀 收到代理动作请求: \(message)")
            await handleUTProxyActionRequest(message)

        case .utProxyStatus:
            logger.info("📈 收到代理状态请求: \(message)")
            await handleUTProxyStatusRequest(message)

        case .utDefault:
            logger.info("📝 收到默认请求: \(message)")
            await handleUTDefaultRequest(message)

        case .UNRECOGNIZED(_):
            logger.warning("⚠️ 收到未识别的 UT 消息类型: \(message)")
        }
    }

    // MARK: - UT 消息处理方法

    /// 处理 UT 状态请求
    private func handleUTStatusRequest(_ message: IAnts_UTMessage) async {
        logger.info("📊 处理 UT 状态请求")

        // 获取 UtunProxy 状态
        let utunInfo = await UtunProxy.shared.getUtunInfo()

        // 构造状态响应消息
        var responseStatus = IAnts_UT_StatusMessage()
        responseStatus.utunCreated = (utunInfo.status == .open)
        responseStatus.interfaceName = utunInfo.name
        responseStatus.unit = utunInfo.fileDescriptor
        responseStatus.interfaceUp = (utunInfo.status == .open)

        // 处理代理状态
        switch utunInfo.proxyStatus {
        case .running(_):
            responseStatus.proxyRunning = true
        case .stopped, .error(_):
            responseStatus.proxyRunning = false
        }

        // 发送响应
        var responseMessage = IAnts_UTMessage()
        responseMessage.messageType = .utType(.utStatus)
        responseMessage.body = .utStatus(responseStatus)

        await sendResponse(responseMessage)
    }

    /// 处理获取配置请求
    private func handleUTGetConfigRequest(_ message: IAnts_UTMessage) async {
        logger.info("⚙️ 处理获取配置请求")
        // TODO: 实现获取配置逻辑
    }

    /// 处理路由配置请求
    private func handleUTRouterConfigRequest(_ message: IAnts_UTMessage) async {
        guard let body = message.body, case .utRouterConfig(let routerConfig) = body else {
            logger.error("❌ 路由配置消息格式错误")
            return
        }

        logger.info("🌐 处理路由配置请求: \(routerConfig)")
        // TODO: 实现路由配置逻辑
    }

    /// 处理路由动作请求
    private func handleUTRouterActionRequest(_ message: IAnts_UTMessage) async {
        guard let body = message.body, case .utRouterAction(let routerAction) = body else {
            logger.error("❌ 路由动作消息格式错误")
            return
        }

        logger.info("🔄 处理路由动作请求: \(routerAction)")
        // TODO: 实现路由动作逻辑
    }

    /// 处理IP配置请求
    private func handleUTIPConfigRequest(_ message: IAnts_UTMessage) async {
        guard let body = message.body, case .utIpConfig(let ipConfig) = body else {
            logger.error("❌ IP配置消息格式错误")
            return
        }

        logger.info("🌍 处理IP配置请求: \(ipConfig)")

        do {
            // 创建UTUN设备
            try await UtunProxy.shared.createUtun()

            // 配置UTUN接口
            try await UtunProxy.shared.configureUtun(
                localAddr: ipConfig.ipAddress,
                remoteAddr: ipConfig.destAddress,
                mtu: Int(ipConfig.mtu)
            )

            logger.info("✅ IP配置成功")
        } catch {
            logger.error("❌ IP配置失败: \(error)")
        }
    }

    /// 处理代理配置请求
    private func handleUTProxyConfigRequest(_ message: IAnts_UTMessage) async {
        guard let body = message.body, case .utProxyConfig(let proxyConfig) = body else {
            logger.error("❌ 代理配置消息格式错误")
            return
        }

        logger.info("🔧 处理代理配置请求: \(proxyConfig)")
        // TODO: 保存代理配置
    }

    /// 处理代理动作请求
    private func handleUTProxyActionRequest(_ message: IAnts_UTMessage) async {
        guard let body = message.body, case .utProxyAction(let proxyAction) = body else {
            logger.error("❌ 代理动作消息格式错误")
            return
        }

        logger.info("🚀 处理代理动作请求: \(proxyAction)")

        do {
            switch proxyAction.action {
            case .start:
                // 使用默认代理URL，实际应用中可以从配置中获取
                let defaultProxyUrl = "socks5://127.0.0.1:1080"
                try await UtunProxy.shared.startProxy(proxyUrl: defaultProxyUrl)
                logger.info("✅ 代理启动成功")

            case .stop:
                await UtunProxy.shared.stopProxy()
                logger.info("✅ 代理停止成功")

            case .restart:
                // 使用默认代理URL，实际应用中可以从配置中获取
                let defaultProxyUrl = "socks5://127.0.0.1:1080"
                try await UtunProxy.shared.restartProxy(proxyUrl: defaultProxyUrl)
                logger.info("✅ 代理重启成功")

            case .UNRECOGNIZED(_):
                logger.warning("⚠️ 未知的代理动作: \(proxyAction.action)")
            }
        } catch {
            logger.error("❌ 代理动作执行失败: \(error)")
        }
    }

    /// 处理代理状态请求
    private func handleUTProxyStatusRequest(_ message: IAnts_UTMessage) async {
        logger.info("📈 处理代理状态请求")

        let utunInfo = await UtunProxy.shared.getUtunInfo()

        // 构造代理状态响应消息
        var responseStatus = IAnts_UT_ProxyStatusMessage()
        switch utunInfo.proxyStatus {
        case .running(let url):
            responseStatus.running = true
            responseStatus.proxyURL = url
        case .stopped, .error(_):
            responseStatus.running = false
        }
        responseStatus.startTime = 0 // TODO: 添加启动时间记录

        // 发送响应
        var responseMessage = IAnts_UTMessage()
        responseMessage.messageType = .utType(.utProxyStatus)
        responseMessage.body = .utProxyStatus(responseStatus)

        await sendResponse(responseMessage)
    }

    /// 处理默认请求
    private func handleUTDefaultRequest(_ message: IAnts_UTMessage) async {
        logger.info("📝 处理默认请求")
        // 默认处理逻辑
    }

    // MARK: - 响应消息发送

    /// 发送响应消息
    private func sendResponse(_ responseMessage: IAnts_UTMessage) async {
        guard let client = wsClient else {
            logger.error("❌ WebSocket 客户端未连接，无法发送响应")
            return
        }

        do {
            let data = try responseMessage.serializedData()
            try await client.sendProtobufData(data)
            logger.debug("📤 发送响应消息成功")
        } catch {
            logger.error("❌ 发送响应消息失败: \(error)")
        }
    }
}

// MARK: - WSClientDelegate
extension RemoteClient: WSClientDelegate {
    func wsClientDidConnect(_ client: WSClient) {
        // 连接成功，重置重连间隔
        currentRetryInterval = baseRetryInterval
        logger.info("✅ WebSocket 连接成功")

        // 启动心跳
        startHeartbeat(client: client)
    }

    func wsClientDidDisconnect(_ client: WSClient, error: Error?) {
        // 连接断开或出错，停止心跳
        stopHeartbeat()
        if let error = error {
            logger.error("❌ WebSocket 连接断开: \(error)")
        } else {
            logger.info("❌ WebSocket 连接断开")
        }

        // 如果不是手动断开，触发重连
        if isStarted {
            logger.info("🔄 连接断开，将触发重连...")
        }
    }

    func wsClient(_ client: WSClient, didReceiveData data: Data) {
        // 使用独立的后台队列处理消息，避免阻塞其他任务
        Task.detached { [weak self] in
            await self?.handleMessage(data)
        }
    }

    func wsClient(_ client: WSClient, didReceiveError error: Error) {
        logger.error("❌ WebSocket 错误: \(error)")
    }
}
