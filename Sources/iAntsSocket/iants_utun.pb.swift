// DO NOT EDIT.
// swift-format-ignore-file
// swiftlint:disable all
//
// Generated by the Swift generator plugin for the protocol buffer compiler.
// Source: iants_utun.proto
//
// For information on using the generated types, please see the documentation:
//   https://github.com/apple/swift-protobuf/

import SwiftProtobuf

// If the compiler emits an error on this type, it is because this file
// was generated by a version of the `protoc` Swift plug-in that is
// incompatible with the version of SwiftProtobuf to which you are linking.
// Please ensure that you are building against the same version of the API
// that was used to generate this file.
fileprivate struct _GeneratedWithProtocGenSwiftVersion: SwiftProtobuf.ProtobufAPIVersionCheck {
  struct _2: SwiftProtobuf.ProtobufAPIVersion_2 {}
  typealias Version = _2
}

/// UT 专用消息类型
enum IAnts_UTMessageType: SwiftProtobuf.Enum, Swift.CaseIterable {
  typealias RawValue = Int
  case utStatus // = 0
  case utGetConfig // = 1
  case utRouterConfig // = 2
  case utRouterAction // = 3
  case utIpConfig // = 4
  case utProxyConfig // = 5
  case utProxyAction // = 6
  case utProxyStatus // = 7

  /// 新增三种模式相关消息类型
  case utModeStartPureProxy // = 10

  /// 启动纯抓包模式
  case utModeStartPureCapture // = 11

  /// 启动代理抓包模式
  case utModeStartProxyCapture // = 12

  /// 停止当前模式
  case utModeStop // = 13

  /// 获取模式状态
  case utModeStatus // = 14

  /// 配置管理消息类型
  case utConfigSetRouteWhitelist // = 20

  /// 设置路由黑名单
  case utConfigSetRouteBlacklist // = 21

  /// 设置代理白名单
  case utConfigSetProxyWhitelist // = 22

  /// 设置代理黑名单
  case utConfigSetProxyBlacklist // = 23

  /// 获取所有配置
  case utConfigGetAll // = 24

  /// 获取路由表信息
  case utConfigGetRoutingTable // = 25
  case utDefault // = 99
  case UNRECOGNIZED(Int)

  init() {
    self = .utStatus
  }

  init?(rawValue: Int) {
    switch rawValue {
    case 0: self = .utStatus
    case 1: self = .utGetConfig
    case 2: self = .utRouterConfig
    case 3: self = .utRouterAction
    case 4: self = .utIpConfig
    case 5: self = .utProxyConfig
    case 6: self = .utProxyAction
    case 7: self = .utProxyStatus
    case 10: self = .utModeStartPureProxy
    case 11: self = .utModeStartPureCapture
    case 12: self = .utModeStartProxyCapture
    case 13: self = .utModeStop
    case 14: self = .utModeStatus
    case 20: self = .utConfigSetRouteWhitelist
    case 21: self = .utConfigSetRouteBlacklist
    case 22: self = .utConfigSetProxyWhitelist
    case 23: self = .utConfigSetProxyBlacklist
    case 24: self = .utConfigGetAll
    case 25: self = .utConfigGetRoutingTable
    case 99: self = .utDefault
    default: self = .UNRECOGNIZED(rawValue)
    }
  }

  var rawValue: Int {
    switch self {
    case .utStatus: return 0
    case .utGetConfig: return 1
    case .utRouterConfig: return 2
    case .utRouterAction: return 3
    case .utIpConfig: return 4
    case .utProxyConfig: return 5
    case .utProxyAction: return 6
    case .utProxyStatus: return 7
    case .utModeStartPureProxy: return 10
    case .utModeStartPureCapture: return 11
    case .utModeStartProxyCapture: return 12
    case .utModeStop: return 13
    case .utModeStatus: return 14
    case .utConfigSetRouteWhitelist: return 20
    case .utConfigSetRouteBlacklist: return 21
    case .utConfigSetProxyWhitelist: return 22
    case .utConfigSetProxyBlacklist: return 23
    case .utConfigGetAll: return 24
    case .utConfigGetRoutingTable: return 25
    case .utDefault: return 99
    case .UNRECOGNIZED(let i): return i
    }
  }

  // The compiler won't synthesize support with the UNRECOGNIZED case.
  static let allCases: [IAnts_UTMessageType] = [
    .utStatus,
    .utGetConfig,
    .utRouterConfig,
    .utRouterAction,
    .utIpConfig,
    .utProxyConfig,
    .utProxyAction,
    .utProxyStatus,
    .utModeStartPureProxy,
    .utModeStartPureCapture,
    .utModeStartProxyCapture,
    .utModeStop,
    .utModeStatus,
    .utConfigSetRouteWhitelist,
    .utConfigSetRouteBlacklist,
    .utConfigSetProxyWhitelist,
    .utConfigSetProxyBlacklist,
    .utConfigGetAll,
    .utConfigGetRoutingTable,
    .utDefault,
  ]

}

/// 代理模式枚举
enum IAnts_ProxyMode: SwiftProtobuf.Enum, Swift.CaseIterable {
  typealias RawValue = Int

  /// 纯代理模式
  case pureProxy // = 0

  /// 纯抓包模式
  case pureCapture // = 1

  /// 代理抓包模式
  case proxyCapture // = 2
  case UNRECOGNIZED(Int)

  init() {
    self = .pureProxy
  }

  init?(rawValue: Int) {
    switch rawValue {
    case 0: self = .pureProxy
    case 1: self = .pureCapture
    case 2: self = .proxyCapture
    default: self = .UNRECOGNIZED(rawValue)
    }
  }

  var rawValue: Int {
    switch self {
    case .pureProxy: return 0
    case .pureCapture: return 1
    case .proxyCapture: return 2
    case .UNRECOGNIZED(let i): return i
    }
  }

  // The compiler won't synthesize support with the UNRECOGNIZED case.
  static let allCases: [IAnts_ProxyMode] = [
    .pureProxy,
    .pureCapture,
    .proxyCapture,
  ]

}

/// 代理状态枚举
enum IAnts_ProxyModeState: SwiftProtobuf.Enum, Swift.CaseIterable {
  typealias RawValue = Int
  case modeStopped // = 0
  case modeStarting // = 1
  case modeRunning // = 2
  case modeStopping // = 3
  case modeError // = 4
  case UNRECOGNIZED(Int)

  init() {
    self = .modeStopped
  }

  init?(rawValue: Int) {
    switch rawValue {
    case 0: self = .modeStopped
    case 1: self = .modeStarting
    case 2: self = .modeRunning
    case 3: self = .modeStopping
    case 4: self = .modeError
    default: self = .UNRECOGNIZED(rawValue)
    }
  }

  var rawValue: Int {
    switch self {
    case .modeStopped: return 0
    case .modeStarting: return 1
    case .modeRunning: return 2
    case .modeStopping: return 3
    case .modeError: return 4
    case .UNRECOGNIZED(let i): return i
    }
  }

  // The compiler won't synthesize support with the UNRECOGNIZED case.
  static let allCases: [IAnts_ProxyModeState] = [
    .modeStopped,
    .modeStarting,
    .modeRunning,
    .modeStopping,
    .modeError,
  ]

}

/// UT 专用消息体
struct IAnts_UT_StatusMessage: Sendable {
  // SwiftProtobuf.Message conformance is added in an extension below. See the
  // `Message` and `Message+*Additions` files in the SwiftProtobuf library for
  // methods supported on all messages.

  var utunCreated: Bool = false

  var interfaceName: String = String()

  var unit: Int32 = 0

  var ipAddress: String = String()

  var destAddress: String = String()

  var mtu: Int32 = 0

  var interfaceUp: Bool = false

  var proxyRunning: Bool = false

  var proxyURL: String = String()

  var routes: [String] = []

  var unknownFields = SwiftProtobuf.UnknownStorage()

  init() {}
}

/// 空消息
struct IAnts_UT_GetConfigMessage: Sendable {
  // SwiftProtobuf.Message conformance is added in an extension below. See the
  // `Message` and `Message+*Additions` files in the SwiftProtobuf library for
  // methods supported on all messages.

  var unknownFields = SwiftProtobuf.UnknownStorage()

  init() {}
}

struct IAnts_UT_RouterConfigMessage: Sendable {
  // SwiftProtobuf.Message conformance is added in an extension below. See the
  // `Message` and `Message+*Additions` files in the SwiftProtobuf library for
  // methods supported on all messages.

  var strategy: String = String()

  var whitelist: [String] = []

  var blacklist: [String] = []

  var enableDnsHijack: Bool = false

  var dnsServer: String = String()

  var unknownFields = SwiftProtobuf.UnknownStorage()

  init() {}
}

struct IAnts_UT_RouterActionMessage: Sendable {
  // SwiftProtobuf.Message conformance is added in an extension below. See the
  // `Message` and `Message+*Additions` files in the SwiftProtobuf library for
  // methods supported on all messages.

  var action: IAnts_UT_RouterActionMessage.Action = .add

  var destination: String = String()

  var gateway: String = String()

  var netmask: String = String()

  var interface: String = String()

  var metric: Int32 = 0

  var unknownFields = SwiftProtobuf.UnknownStorage()

  enum Action: SwiftProtobuf.Enum, Swift.CaseIterable {
    typealias RawValue = Int
    case add // = 0
    case delete // = 1
    case modify // = 2
    case restoreDefault // = 3
    case UNRECOGNIZED(Int)

    init() {
      self = .add
    }

    init?(rawValue: Int) {
      switch rawValue {
      case 0: self = .add
      case 1: self = .delete
      case 2: self = .modify
      case 3: self = .restoreDefault
      default: self = .UNRECOGNIZED(rawValue)
      }
    }

    var rawValue: Int {
      switch self {
      case .add: return 0
      case .delete: return 1
      case .modify: return 2
      case .restoreDefault: return 3
      case .UNRECOGNIZED(let i): return i
      }
    }

    // The compiler won't synthesize support with the UNRECOGNIZED case.
    static let allCases: [IAnts_UT_RouterActionMessage.Action] = [
      .add,
      .delete,
      .modify,
      .restoreDefault,
    ]

  }

  init() {}
}

struct IAnts_UT_IPConfigMessage: Sendable {
  // SwiftProtobuf.Message conformance is added in an extension below. See the
  // `Message` and `Message+*Additions` files in the SwiftProtobuf library for
  // methods supported on all messages.

  var ipAddress: String = String()

  var destAddress: String = String()

  var netmask: String = String()

  var mtu: Int32 = 0

  var bringUp: Bool = false

  var unknownFields = SwiftProtobuf.UnknownStorage()

  init() {}
}

struct IAnts_UT_ProxyConfigMessage: Sendable {
  // SwiftProtobuf.Message conformance is added in an extension below. See the
  // `Message` and `Message+*Additions` files in the SwiftProtobuf library for
  // methods supported on all messages.

  var type: IAnts_UT_ProxyConfigMessage.ProxyType = .http

  var host: String = String()

  var port: Int32 = 0

  var username: String = String()

  var password: String = String()

  var extraConfig: String = String()

  var unknownFields = SwiftProtobuf.UnknownStorage()

  enum ProxyType: SwiftProtobuf.Enum, Swift.CaseIterable {
    typealias RawValue = Int
    case http // = 0
    case https // = 1
    case socks5 // = 2
    case shadowsocks // = 3
    case UNRECOGNIZED(Int)

    init() {
      self = .http
    }

    init?(rawValue: Int) {
      switch rawValue {
      case 0: self = .http
      case 1: self = .https
      case 2: self = .socks5
      case 3: self = .shadowsocks
      default: self = .UNRECOGNIZED(rawValue)
      }
    }

    var rawValue: Int {
      switch self {
      case .http: return 0
      case .https: return 1
      case .socks5: return 2
      case .shadowsocks: return 3
      case .UNRECOGNIZED(let i): return i
      }
    }

    // The compiler won't synthesize support with the UNRECOGNIZED case.
    static let allCases: [IAnts_UT_ProxyConfigMessage.ProxyType] = [
      .http,
      .https,
      .socks5,
      .shadowsocks,
    ]

  }

  init() {}
}

struct IAnts_UT_ProxyActionMessage: Sendable {
  // SwiftProtobuf.Message conformance is added in an extension below. See the
  // `Message` and `Message+*Additions` files in the SwiftProtobuf library for
  // methods supported on all messages.

  var action: IAnts_UT_ProxyActionMessage.Action = .start

  var unknownFields = SwiftProtobuf.UnknownStorage()

  enum Action: SwiftProtobuf.Enum, Swift.CaseIterable {
    typealias RawValue = Int
    case start // = 0
    case stop // = 1
    case restart // = 2
    case UNRECOGNIZED(Int)

    init() {
      self = .start
    }

    init?(rawValue: Int) {
      switch rawValue {
      case 0: self = .start
      case 1: self = .stop
      case 2: self = .restart
      default: self = .UNRECOGNIZED(rawValue)
      }
    }

    var rawValue: Int {
      switch self {
      case .start: return 0
      case .stop: return 1
      case .restart: return 2
      case .UNRECOGNIZED(let i): return i
      }
    }

    // The compiler won't synthesize support with the UNRECOGNIZED case.
    static let allCases: [IAnts_UT_ProxyActionMessage.Action] = [
      .start,
      .stop,
      .restart,
    ]

  }

  init() {}
}

struct IAnts_UT_ProxyStatusMessage: Sendable {
  // SwiftProtobuf.Message conformance is added in an extension below. See the
  // `Message` and `Message+*Additions` files in the SwiftProtobuf library for
  // methods supported on all messages.

  var running: Bool = false

  var proxyURL: String = String()

  var bytesSent: Int64 = 0

  var bytesReceived: Int64 = 0

  var startTime: Int64 = 0

  var unknownFields = SwiftProtobuf.UnknownStorage()

  init() {}
}

/// 空消息
struct IAnts_UT_DefaultMessage: Sendable {
  // SwiftProtobuf.Message conformance is added in an extension below. See the
  // `Message` and `Message+*Additions` files in the SwiftProtobuf library for
  // methods supported on all messages.

  var unknownFields = SwiftProtobuf.UnknownStorage()

  init() {}
}

/// 过滤配置
struct IAnts_FilterConfig: Sendable {
  // SwiftProtobuf.Message conformance is added in an extension below. See the
  // `Message` and `Message+*Additions` files in the SwiftProtobuf library for
  // methods supported on all messages.

  /// 域名白名单
  var allowedDomains: [String] = []

  /// 域名黑名单
  var blockedDomains: [String] = []

  /// IP白名单 (支持CIDR)
  var allowedIps: [String] = []

  /// IP黑名单 (支持CIDR)
  var blockedIps: [String] = []

  var unknownFields = SwiftProtobuf.UnknownStorage()

  init() {}
}

/// 纯代理模式启动消息
struct IAnts_UT_StartPureProxyMessage: Sendable {
  // SwiftProtobuf.Message conformance is added in an extension below. See the
  // `Message` and `Message+*Additions` files in the SwiftProtobuf library for
  // methods supported on all messages.

  /// 代理地址 (必填)
  var proxyURL: String = String()

  /// 是否启动UDP转发 (默认true)
  var enableUdp: Bool = false

  /// 是否全局代理 (默认false)
  var globalProxy: Bool = false

  var unknownFields = SwiftProtobuf.UnknownStorage()

  init() {}
}

/// 纯抓包模式启动消息
struct IAnts_UT_StartPureCaptureMessage: Sendable {
  // SwiftProtobuf.Message conformance is added in an extension below. See the
  // `Message` and `Message+*Additions` files in the SwiftProtobuf library for
  // methods supported on all messages.

  /// 过滤配置
  var filterConfig: IAnts_FilterConfig {
    get {return _filterConfig ?? IAnts_FilterConfig()}
    set {_filterConfig = newValue}
  }
  /// Returns true if `filterConfig` has been explicitly set.
  var hasFilterConfig: Bool {return self._filterConfig != nil}
  /// Clears the value of `filterConfig`. Subsequent reads from it will return its default value.
  mutating func clearFilterConfig() {self._filterConfig = nil}

  /// 远程WebSocket服务器 (可选)
  var remoteWsServer: String = String()

  /// 是否启动UDP转发 (默认true)
  var enableUdp: Bool = false

  /// 是否全局代理 (默认false)
  var globalProxy: Bool = false

  var unknownFields = SwiftProtobuf.UnknownStorage()

  init() {}

  fileprivate var _filterConfig: IAnts_FilterConfig? = nil
}

/// 代理抓包模式启动消息
struct IAnts_UT_StartProxyCaptureMessage: Sendable {
  // SwiftProtobuf.Message conformance is added in an extension below. See the
  // `Message` and `Message+*Additions` files in the SwiftProtobuf library for
  // methods supported on all messages.

  /// 过滤配置
  var filterConfig: IAnts_FilterConfig {
    get {return _filterConfig ?? IAnts_FilterConfig()}
    set {_filterConfig = newValue}
  }
  /// Returns true if `filterConfig` has been explicitly set.
  var hasFilterConfig: Bool {return self._filterConfig != nil}
  /// Clears the value of `filterConfig`. Subsequent reads from it will return its default value.
  mutating func clearFilterConfig() {self._filterConfig = nil}

  /// 代理地址 (必填)
  var proxyURL: String = String()

  /// 是否启动UDP转发 (默认true)
  var enableUdp: Bool = false

  /// 是否全局代理 (默认false)
  var globalProxy: Bool = false

  /// 远程WebSocket服务器 (可选)
  var remoteWsServer: String = String()

  var unknownFields = SwiftProtobuf.UnknownStorage()

  init() {}

  fileprivate var _filterConfig: IAnts_FilterConfig? = nil
}

/// 停止模式消息
struct IAnts_UT_StopModeMessage: Sendable {
  // SwiftProtobuf.Message conformance is added in an extension below. See the
  // `Message` and `Message+*Additions` files in the SwiftProtobuf library for
  // methods supported on all messages.

  var unknownFields = SwiftProtobuf.UnknownStorage()

  init() {}
}

/// 模式状态消息
struct IAnts_UT_ModeStatusMessage: Sendable {
  // SwiftProtobuf.Message conformance is added in an extension below. See the
  // `Message` and `Message+*Additions` files in the SwiftProtobuf library for
  // methods supported on all messages.

  /// 当前状态
  var state: IAnts_ProxyModeState = .modeStopped

  /// 当前模式 (仅在运行时有效)
  var mode: IAnts_ProxyMode = .pureProxy

  /// 错误信息 (仅在错误状态时有效)
  var errorMessage: String = String()

  /// 启动时间戳
  var startTime: Int64 = 0

  /// 代理URL (如果适用)
  var proxyURL: String = String()

  /// 远程WebSocket服务器 (如果适用)
  var remoteWsServer: String = String()

  /// UDP转发状态
  var enableUdp: Bool = false

  /// 全局代理状态
  var globalProxy: Bool = false

  var unknownFields = SwiftProtobuf.UnknownStorage()

  init() {}
}

/// 设置路由白名单消息
struct IAnts_UT_SetRouteWhitelistMessage: Sendable {
  // SwiftProtobuf.Message conformance is added in an extension below. See the
  // `Message` and `Message+*Additions` files in the SwiftProtobuf library for
  // methods supported on all messages.

  /// 路由白名单
  var whitelist: [String] = []

  var unknownFields = SwiftProtobuf.UnknownStorage()

  init() {}
}

/// 设置路由黑名单消息
struct IAnts_UT_SetRouteBlacklistMessage: Sendable {
  // SwiftProtobuf.Message conformance is added in an extension below. See the
  // `Message` and `Message+*Additions` files in the SwiftProtobuf library for
  // methods supported on all messages.

  /// 路由黑名单
  var blacklist: [String] = []

  var unknownFields = SwiftProtobuf.UnknownStorage()

  init() {}
}

/// 设置代理白名单消息
struct IAnts_UT_SetProxyWhitelistMessage: Sendable {
  // SwiftProtobuf.Message conformance is added in an extension below. See the
  // `Message` and `Message+*Additions` files in the SwiftProtobuf library for
  // methods supported on all messages.

  /// 代理白名单
  var whitelist: [String] = []

  var unknownFields = SwiftProtobuf.UnknownStorage()

  init() {}
}

/// 设置代理黑名单消息
struct IAnts_UT_SetProxyBlacklistMessage: Sendable {
  // SwiftProtobuf.Message conformance is added in an extension below. See the
  // `Message` and `Message+*Additions` files in the SwiftProtobuf library for
  // methods supported on all messages.

  /// 代理黑名单
  var blacklist: [String] = []

  var unknownFields = SwiftProtobuf.UnknownStorage()

  init() {}
}

/// 获取所有配置消息
struct IAnts_UT_GetAllConfigMessage: Sendable {
  // SwiftProtobuf.Message conformance is added in an extension below. See the
  // `Message` and `Message+*Additions` files in the SwiftProtobuf library for
  // methods supported on all messages.

  var unknownFields = SwiftProtobuf.UnknownStorage()

  init() {}
}

/// 所有配置响应消息
struct IAnts_UT_AllConfigResponseMessage: Sendable {
  // SwiftProtobuf.Message conformance is added in an extension below. See the
  // `Message` and `Message+*Additions` files in the SwiftProtobuf library for
  // methods supported on all messages.

  /// 路由白名单
  var routeWhitelist: [String] = []

  /// 路由黑名单
  var routeBlacklist: [String] = []

  /// 代理白名单
  var proxyWhitelist: [String] = []

  /// 代理黑名单
  var proxyBlacklist: [String] = []

  var unknownFields = SwiftProtobuf.UnknownStorage()

  init() {}
}

/// 获取路由表消息
struct IAnts_UT_GetRoutingTableMessage: Sendable {
  // SwiftProtobuf.Message conformance is added in an extension below. See the
  // `Message` and `Message+*Additions` files in the SwiftProtobuf library for
  // methods supported on all messages.

  var unknownFields = SwiftProtobuf.UnknownStorage()

  init() {}
}

/// 路由表条目
struct IAnts_RouteEntry: Sendable {
  // SwiftProtobuf.Message conformance is added in an extension below. See the
  // `Message` and `Message+*Additions` files in the SwiftProtobuf library for
  // methods supported on all messages.

  /// 目标地址
  var destination: String = String()

  /// 网关
  var gateway: String = String()

  /// 网络掩码
  var netmask: String = String()

  /// 接口名称
  var interface: String = String()

  /// 路由标志
  var flags: Int32 = 0

  /// 路由度量
  var metric: Int32 = 0

  /// 是否IPv6
  var isIpv6: Bool = false

  /// 是否有MAC地址
  var hasMac_p: Bool = false

  /// MAC地址
  var macAddress: String = String()

  var unknownFields = SwiftProtobuf.UnknownStorage()

  init() {}
}

/// 路由表响应消息
struct IAnts_UT_RoutingTableResponseMessage: Sendable {
  // SwiftProtobuf.Message conformance is added in an extension below. See the
  // `Message` and `Message+*Additions` files in the SwiftProtobuf library for
  // methods supported on all messages.

  /// 路由表条目
  var routes: [IAnts_RouteEntry] = []

  var unknownFields = SwiftProtobuf.UnknownStorage()

  init() {}
}

/// UT 完整消息
struct IAnts_UTMessage: Sendable {
  // SwiftProtobuf.Message conformance is added in an extension below. See the
  // `Message` and `Message+*Additions` files in the SwiftProtobuf library for
  // methods supported on all messages.

  var messageType: IAnts_UTMessage.OneOf_MessageType? = nil

  var commonType: IAnts_CommonMessageType {
    get {
      if case .commonType(let v)? = messageType {return v}
      return .heartbeat
    }
    set {messageType = .commonType(newValue)}
  }

  var utType: IAnts_UTMessageType {
    get {
      if case .utType(let v)? = messageType {return v}
      return .utStatus
    }
    set {messageType = .utType(newValue)}
  }

  var body: IAnts_UTMessage.OneOf_Body? = nil

  /// 公共消息
  var heartbeat: IAnts_Heartbeat {
    get {
      if case .heartbeat(let v)? = body {return v}
      return IAnts_Heartbeat()
    }
    set {body = .heartbeat(newValue)}
  }

  var error: IAnts_ErrorMessage {
    get {
      if case .error(let v)? = body {return v}
      return IAnts_ErrorMessage()
    }
    set {body = .error(newValue)}
  }

  /// UT 专用消息 - 原有消息
  var utStatus: IAnts_UT_StatusMessage {
    get {
      if case .utStatus(let v)? = body {return v}
      return IAnts_UT_StatusMessage()
    }
    set {body = .utStatus(newValue)}
  }

  var utGetConfig: IAnts_UT_GetConfigMessage {
    get {
      if case .utGetConfig(let v)? = body {return v}
      return IAnts_UT_GetConfigMessage()
    }
    set {body = .utGetConfig(newValue)}
  }

  var utRouterConfig: IAnts_UT_RouterConfigMessage {
    get {
      if case .utRouterConfig(let v)? = body {return v}
      return IAnts_UT_RouterConfigMessage()
    }
    set {body = .utRouterConfig(newValue)}
  }

  var utRouterAction: IAnts_UT_RouterActionMessage {
    get {
      if case .utRouterAction(let v)? = body {return v}
      return IAnts_UT_RouterActionMessage()
    }
    set {body = .utRouterAction(newValue)}
  }

  var utIpConfig: IAnts_UT_IPConfigMessage {
    get {
      if case .utIpConfig(let v)? = body {return v}
      return IAnts_UT_IPConfigMessage()
    }
    set {body = .utIpConfig(newValue)}
  }

  var utProxyConfig: IAnts_UT_ProxyConfigMessage {
    get {
      if case .utProxyConfig(let v)? = body {return v}
      return IAnts_UT_ProxyConfigMessage()
    }
    set {body = .utProxyConfig(newValue)}
  }

  var utProxyAction: IAnts_UT_ProxyActionMessage {
    get {
      if case .utProxyAction(let v)? = body {return v}
      return IAnts_UT_ProxyActionMessage()
    }
    set {body = .utProxyAction(newValue)}
  }

  var utProxyStatus: IAnts_UT_ProxyStatusMessage {
    get {
      if case .utProxyStatus(let v)? = body {return v}
      return IAnts_UT_ProxyStatusMessage()
    }
    set {body = .utProxyStatus(newValue)}
  }

  var utDefault: IAnts_UT_DefaultMessage {
    get {
      if case .utDefault(let v)? = body {return v}
      return IAnts_UT_DefaultMessage()
    }
    set {body = .utDefault(newValue)}
  }

  /// 新增：三种模式相关消息
  var utStartPureProxy: IAnts_UT_StartPureProxyMessage {
    get {
      if case .utStartPureProxy(let v)? = body {return v}
      return IAnts_UT_StartPureProxyMessage()
    }
    set {body = .utStartPureProxy(newValue)}
  }

  var utStartPureCapture: IAnts_UT_StartPureCaptureMessage {
    get {
      if case .utStartPureCapture(let v)? = body {return v}
      return IAnts_UT_StartPureCaptureMessage()
    }
    set {body = .utStartPureCapture(newValue)}
  }

  var utStartProxyCapture: IAnts_UT_StartProxyCaptureMessage {
    get {
      if case .utStartProxyCapture(let v)? = body {return v}
      return IAnts_UT_StartProxyCaptureMessage()
    }
    set {body = .utStartProxyCapture(newValue)}
  }

  var utStopMode: IAnts_UT_StopModeMessage {
    get {
      if case .utStopMode(let v)? = body {return v}
      return IAnts_UT_StopModeMessage()
    }
    set {body = .utStopMode(newValue)}
  }

  var utModeStatus: IAnts_UT_ModeStatusMessage {
    get {
      if case .utModeStatus(let v)? = body {return v}
      return IAnts_UT_ModeStatusMessage()
    }
    set {body = .utModeStatus(newValue)}
  }

  /// 新增：配置管理消息
  var utSetRouteWhitelist: IAnts_UT_SetRouteWhitelistMessage {
    get {
      if case .utSetRouteWhitelist(let v)? = body {return v}
      return IAnts_UT_SetRouteWhitelistMessage()
    }
    set {body = .utSetRouteWhitelist(newValue)}
  }

  var utSetRouteBlacklist: IAnts_UT_SetRouteBlacklistMessage {
    get {
      if case .utSetRouteBlacklist(let v)? = body {return v}
      return IAnts_UT_SetRouteBlacklistMessage()
    }
    set {body = .utSetRouteBlacklist(newValue)}
  }

  var utSetProxyWhitelist: IAnts_UT_SetProxyWhitelistMessage {
    get {
      if case .utSetProxyWhitelist(let v)? = body {return v}
      return IAnts_UT_SetProxyWhitelistMessage()
    }
    set {body = .utSetProxyWhitelist(newValue)}
  }

  var utSetProxyBlacklist: IAnts_UT_SetProxyBlacklistMessage {
    get {
      if case .utSetProxyBlacklist(let v)? = body {return v}
      return IAnts_UT_SetProxyBlacklistMessage()
    }
    set {body = .utSetProxyBlacklist(newValue)}
  }

  var utGetAllConfig: IAnts_UT_GetAllConfigMessage {
    get {
      if case .utGetAllConfig(let v)? = body {return v}
      return IAnts_UT_GetAllConfigMessage()
    }
    set {body = .utGetAllConfig(newValue)}
  }

  var utAllConfigResponse: IAnts_UT_AllConfigResponseMessage {
    get {
      if case .utAllConfigResponse(let v)? = body {return v}
      return IAnts_UT_AllConfigResponseMessage()
    }
    set {body = .utAllConfigResponse(newValue)}
  }

  var utGetRoutingTable: IAnts_UT_GetRoutingTableMessage {
    get {
      if case .utGetRoutingTable(let v)? = body {return v}
      return IAnts_UT_GetRoutingTableMessage()
    }
    set {body = .utGetRoutingTable(newValue)}
  }

  var utRoutingTableResponse: IAnts_UT_RoutingTableResponseMessage {
    get {
      if case .utRoutingTableResponse(let v)? = body {return v}
      return IAnts_UT_RoutingTableResponseMessage()
    }
    set {body = .utRoutingTableResponse(newValue)}
  }

  var unknownFields = SwiftProtobuf.UnknownStorage()

  enum OneOf_MessageType: Equatable, Sendable {
    case commonType(IAnts_CommonMessageType)
    case utType(IAnts_UTMessageType)

  }

  enum OneOf_Body: Equatable, Sendable {
    /// 公共消息
    case heartbeat(IAnts_Heartbeat)
    case error(IAnts_ErrorMessage)
    /// UT 专用消息 - 原有消息
    case utStatus(IAnts_UT_StatusMessage)
    case utGetConfig(IAnts_UT_GetConfigMessage)
    case utRouterConfig(IAnts_UT_RouterConfigMessage)
    case utRouterAction(IAnts_UT_RouterActionMessage)
    case utIpConfig(IAnts_UT_IPConfigMessage)
    case utProxyConfig(IAnts_UT_ProxyConfigMessage)
    case utProxyAction(IAnts_UT_ProxyActionMessage)
    case utProxyStatus(IAnts_UT_ProxyStatusMessage)
    case utDefault(IAnts_UT_DefaultMessage)
    /// 新增：三种模式相关消息
    case utStartPureProxy(IAnts_UT_StartPureProxyMessage)
    case utStartPureCapture(IAnts_UT_StartPureCaptureMessage)
    case utStartProxyCapture(IAnts_UT_StartProxyCaptureMessage)
    case utStopMode(IAnts_UT_StopModeMessage)
    case utModeStatus(IAnts_UT_ModeStatusMessage)
    /// 新增：配置管理消息
    case utSetRouteWhitelist(IAnts_UT_SetRouteWhitelistMessage)
    case utSetRouteBlacklist(IAnts_UT_SetRouteBlacklistMessage)
    case utSetProxyWhitelist(IAnts_UT_SetProxyWhitelistMessage)
    case utSetProxyBlacklist(IAnts_UT_SetProxyBlacklistMessage)
    case utGetAllConfig(IAnts_UT_GetAllConfigMessage)
    case utAllConfigResponse(IAnts_UT_AllConfigResponseMessage)
    case utGetRoutingTable(IAnts_UT_GetRoutingTableMessage)
    case utRoutingTableResponse(IAnts_UT_RoutingTableResponseMessage)

  }

  init() {}
}

// MARK: - Code below here is support for the SwiftProtobuf runtime.

fileprivate let _protobuf_package = "iAnts"

extension IAnts_UTMessageType: SwiftProtobuf._ProtoNameProviding {
  static let _protobuf_nameMap: SwiftProtobuf._NameMap = [
    0: .same(proto: "UT_STATUS"),
    1: .same(proto: "UT_GET_CONFIG"),
    2: .same(proto: "UT_ROUTER_CONFIG"),
    3: .same(proto: "UT_ROUTER_ACTION"),
    4: .same(proto: "UT_IP_CONFIG"),
    5: .same(proto: "UT_PROXY_CONFIG"),
    6: .same(proto: "UT_PROXY_ACTION"),
    7: .same(proto: "UT_PROXY_STATUS"),
    10: .same(proto: "UT_MODE_START_PURE_PROXY"),
    11: .same(proto: "UT_MODE_START_PURE_CAPTURE"),
    12: .same(proto: "UT_MODE_START_PROXY_CAPTURE"),
    13: .same(proto: "UT_MODE_STOP"),
    14: .same(proto: "UT_MODE_STATUS"),
    20: .same(proto: "UT_CONFIG_SET_ROUTE_WHITELIST"),
    21: .same(proto: "UT_CONFIG_SET_ROUTE_BLACKLIST"),
    22: .same(proto: "UT_CONFIG_SET_PROXY_WHITELIST"),
    23: .same(proto: "UT_CONFIG_SET_PROXY_BLACKLIST"),
    24: .same(proto: "UT_CONFIG_GET_ALL"),
    25: .same(proto: "UT_CONFIG_GET_ROUTING_TABLE"),
    99: .same(proto: "UT_DEFAULT"),
  ]
}

extension IAnts_ProxyMode: SwiftProtobuf._ProtoNameProviding {
  static let _protobuf_nameMap: SwiftProtobuf._NameMap = [
    0: .same(proto: "PURE_PROXY"),
    1: .same(proto: "PURE_CAPTURE"),
    2: .same(proto: "PROXY_CAPTURE"),
  ]
}

extension IAnts_ProxyModeState: SwiftProtobuf._ProtoNameProviding {
  static let _protobuf_nameMap: SwiftProtobuf._NameMap = [
    0: .same(proto: "MODE_STOPPED"),
    1: .same(proto: "MODE_STARTING"),
    2: .same(proto: "MODE_RUNNING"),
    3: .same(proto: "MODE_STOPPING"),
    4: .same(proto: "MODE_ERROR"),
  ]
}

extension IAnts_UT_StatusMessage: SwiftProtobuf.Message, SwiftProtobuf._MessageImplementationBase, SwiftProtobuf._ProtoNameProviding {
  static let protoMessageName: String = _protobuf_package + ".UT_StatusMessage"
  static let _protobuf_nameMap: SwiftProtobuf._NameMap = [
    1: .standard(proto: "utun_created"),
    2: .standard(proto: "interface_name"),
    3: .same(proto: "unit"),
    4: .standard(proto: "ip_address"),
    5: .standard(proto: "dest_address"),
    6: .same(proto: "mtu"),
    7: .standard(proto: "interface_up"),
    8: .standard(proto: "proxy_running"),
    9: .standard(proto: "proxy_url"),
    10: .same(proto: "routes"),
  ]

  mutating func decodeMessage<D: SwiftProtobuf.Decoder>(decoder: inout D) throws {
    while let fieldNumber = try decoder.nextFieldNumber() {
      // The use of inline closures is to circumvent an issue where the compiler
      // allocates stack space for every case branch when no optimizations are
      // enabled. https://github.com/apple/swift-protobuf/issues/1034
      switch fieldNumber {
      case 1: try { try decoder.decodeSingularBoolField(value: &self.utunCreated) }()
      case 2: try { try decoder.decodeSingularStringField(value: &self.interfaceName) }()
      case 3: try { try decoder.decodeSingularInt32Field(value: &self.unit) }()
      case 4: try { try decoder.decodeSingularStringField(value: &self.ipAddress) }()
      case 5: try { try decoder.decodeSingularStringField(value: &self.destAddress) }()
      case 6: try { try decoder.decodeSingularInt32Field(value: &self.mtu) }()
      case 7: try { try decoder.decodeSingularBoolField(value: &self.interfaceUp) }()
      case 8: try { try decoder.decodeSingularBoolField(value: &self.proxyRunning) }()
      case 9: try { try decoder.decodeSingularStringField(value: &self.proxyURL) }()
      case 10: try { try decoder.decodeRepeatedStringField(value: &self.routes) }()
      default: break
      }
    }
  }

  func traverse<V: SwiftProtobuf.Visitor>(visitor: inout V) throws {
    if self.utunCreated != false {
      try visitor.visitSingularBoolField(value: self.utunCreated, fieldNumber: 1)
    }
    if !self.interfaceName.isEmpty {
      try visitor.visitSingularStringField(value: self.interfaceName, fieldNumber: 2)
    }
    if self.unit != 0 {
      try visitor.visitSingularInt32Field(value: self.unit, fieldNumber: 3)
    }
    if !self.ipAddress.isEmpty {
      try visitor.visitSingularStringField(value: self.ipAddress, fieldNumber: 4)
    }
    if !self.destAddress.isEmpty {
      try visitor.visitSingularStringField(value: self.destAddress, fieldNumber: 5)
    }
    if self.mtu != 0 {
      try visitor.visitSingularInt32Field(value: self.mtu, fieldNumber: 6)
    }
    if self.interfaceUp != false {
      try visitor.visitSingularBoolField(value: self.interfaceUp, fieldNumber: 7)
    }
    if self.proxyRunning != false {
      try visitor.visitSingularBoolField(value: self.proxyRunning, fieldNumber: 8)
    }
    if !self.proxyURL.isEmpty {
      try visitor.visitSingularStringField(value: self.proxyURL, fieldNumber: 9)
    }
    if !self.routes.isEmpty {
      try visitor.visitRepeatedStringField(value: self.routes, fieldNumber: 10)
    }
    try unknownFields.traverse(visitor: &visitor)
  }

  static func ==(lhs: IAnts_UT_StatusMessage, rhs: IAnts_UT_StatusMessage) -> Bool {
    if lhs.utunCreated != rhs.utunCreated {return false}
    if lhs.interfaceName != rhs.interfaceName {return false}
    if lhs.unit != rhs.unit {return false}
    if lhs.ipAddress != rhs.ipAddress {return false}
    if lhs.destAddress != rhs.destAddress {return false}
    if lhs.mtu != rhs.mtu {return false}
    if lhs.interfaceUp != rhs.interfaceUp {return false}
    if lhs.proxyRunning != rhs.proxyRunning {return false}
    if lhs.proxyURL != rhs.proxyURL {return false}
    if lhs.routes != rhs.routes {return false}
    if lhs.unknownFields != rhs.unknownFields {return false}
    return true
  }
}

extension IAnts_UT_GetConfigMessage: SwiftProtobuf.Message, SwiftProtobuf._MessageImplementationBase, SwiftProtobuf._ProtoNameProviding {
  static let protoMessageName: String = _protobuf_package + ".UT_GetConfigMessage"
  static let _protobuf_nameMap = SwiftProtobuf._NameMap()

  mutating func decodeMessage<D: SwiftProtobuf.Decoder>(decoder: inout D) throws {
    // Load everything into unknown fields
    while try decoder.nextFieldNumber() != nil {}
  }

  func traverse<V: SwiftProtobuf.Visitor>(visitor: inout V) throws {
    try unknownFields.traverse(visitor: &visitor)
  }

  static func ==(lhs: IAnts_UT_GetConfigMessage, rhs: IAnts_UT_GetConfigMessage) -> Bool {
    if lhs.unknownFields != rhs.unknownFields {return false}
    return true
  }
}

extension IAnts_UT_RouterConfigMessage: SwiftProtobuf.Message, SwiftProtobuf._MessageImplementationBase, SwiftProtobuf._ProtoNameProviding {
  static let protoMessageName: String = _protobuf_package + ".UT_RouterConfigMessage"
  static let _protobuf_nameMap: SwiftProtobuf._NameMap = [
    1: .same(proto: "strategy"),
    2: .same(proto: "whitelist"),
    3: .same(proto: "blacklist"),
    4: .standard(proto: "enable_dns_hijack"),
    5: .standard(proto: "dns_server"),
  ]

  mutating func decodeMessage<D: SwiftProtobuf.Decoder>(decoder: inout D) throws {
    while let fieldNumber = try decoder.nextFieldNumber() {
      // The use of inline closures is to circumvent an issue where the compiler
      // allocates stack space for every case branch when no optimizations are
      // enabled. https://github.com/apple/swift-protobuf/issues/1034
      switch fieldNumber {
      case 1: try { try decoder.decodeSingularStringField(value: &self.strategy) }()
      case 2: try { try decoder.decodeRepeatedStringField(value: &self.whitelist) }()
      case 3: try { try decoder.decodeRepeatedStringField(value: &self.blacklist) }()
      case 4: try { try decoder.decodeSingularBoolField(value: &self.enableDnsHijack) }()
      case 5: try { try decoder.decodeSingularStringField(value: &self.dnsServer) }()
      default: break
      }
    }
  }

  func traverse<V: SwiftProtobuf.Visitor>(visitor: inout V) throws {
    if !self.strategy.isEmpty {
      try visitor.visitSingularStringField(value: self.strategy, fieldNumber: 1)
    }
    if !self.whitelist.isEmpty {
      try visitor.visitRepeatedStringField(value: self.whitelist, fieldNumber: 2)
    }
    if !self.blacklist.isEmpty {
      try visitor.visitRepeatedStringField(value: self.blacklist, fieldNumber: 3)
    }
    if self.enableDnsHijack != false {
      try visitor.visitSingularBoolField(value: self.enableDnsHijack, fieldNumber: 4)
    }
    if !self.dnsServer.isEmpty {
      try visitor.visitSingularStringField(value: self.dnsServer, fieldNumber: 5)
    }
    try unknownFields.traverse(visitor: &visitor)
  }

  static func ==(lhs: IAnts_UT_RouterConfigMessage, rhs: IAnts_UT_RouterConfigMessage) -> Bool {
    if lhs.strategy != rhs.strategy {return false}
    if lhs.whitelist != rhs.whitelist {return false}
    if lhs.blacklist != rhs.blacklist {return false}
    if lhs.enableDnsHijack != rhs.enableDnsHijack {return false}
    if lhs.dnsServer != rhs.dnsServer {return false}
    if lhs.unknownFields != rhs.unknownFields {return false}
    return true
  }
}

extension IAnts_UT_RouterActionMessage: SwiftProtobuf.Message, SwiftProtobuf._MessageImplementationBase, SwiftProtobuf._ProtoNameProviding {
  static let protoMessageName: String = _protobuf_package + ".UT_RouterActionMessage"
  static let _protobuf_nameMap: SwiftProtobuf._NameMap = [
    1: .same(proto: "action"),
    2: .same(proto: "destination"),
    3: .same(proto: "gateway"),
    4: .same(proto: "netmask"),
    5: .same(proto: "interface"),
    6: .same(proto: "metric"),
  ]

  mutating func decodeMessage<D: SwiftProtobuf.Decoder>(decoder: inout D) throws {
    while let fieldNumber = try decoder.nextFieldNumber() {
      // The use of inline closures is to circumvent an issue where the compiler
      // allocates stack space for every case branch when no optimizations are
      // enabled. https://github.com/apple/swift-protobuf/issues/1034
      switch fieldNumber {
      case 1: try { try decoder.decodeSingularEnumField(value: &self.action) }()
      case 2: try { try decoder.decodeSingularStringField(value: &self.destination) }()
      case 3: try { try decoder.decodeSingularStringField(value: &self.gateway) }()
      case 4: try { try decoder.decodeSingularStringField(value: &self.netmask) }()
      case 5: try { try decoder.decodeSingularStringField(value: &self.interface) }()
      case 6: try { try decoder.decodeSingularInt32Field(value: &self.metric) }()
      default: break
      }
    }
  }

  func traverse<V: SwiftProtobuf.Visitor>(visitor: inout V) throws {
    if self.action != .add {
      try visitor.visitSingularEnumField(value: self.action, fieldNumber: 1)
    }
    if !self.destination.isEmpty {
      try visitor.visitSingularStringField(value: self.destination, fieldNumber: 2)
    }
    if !self.gateway.isEmpty {
      try visitor.visitSingularStringField(value: self.gateway, fieldNumber: 3)
    }
    if !self.netmask.isEmpty {
      try visitor.visitSingularStringField(value: self.netmask, fieldNumber: 4)
    }
    if !self.interface.isEmpty {
      try visitor.visitSingularStringField(value: self.interface, fieldNumber: 5)
    }
    if self.metric != 0 {
      try visitor.visitSingularInt32Field(value: self.metric, fieldNumber: 6)
    }
    try unknownFields.traverse(visitor: &visitor)
  }

  static func ==(lhs: IAnts_UT_RouterActionMessage, rhs: IAnts_UT_RouterActionMessage) -> Bool {
    if lhs.action != rhs.action {return false}
    if lhs.destination != rhs.destination {return false}
    if lhs.gateway != rhs.gateway {return false}
    if lhs.netmask != rhs.netmask {return false}
    if lhs.interface != rhs.interface {return false}
    if lhs.metric != rhs.metric {return false}
    if lhs.unknownFields != rhs.unknownFields {return false}
    return true
  }
}

extension IAnts_UT_RouterActionMessage.Action: SwiftProtobuf._ProtoNameProviding {
  static let _protobuf_nameMap: SwiftProtobuf._NameMap = [
    0: .same(proto: "ADD"),
    1: .same(proto: "DELETE"),
    2: .same(proto: "MODIFY"),
    3: .same(proto: "RESTORE_DEFAULT"),
  ]
}

extension IAnts_UT_IPConfigMessage: SwiftProtobuf.Message, SwiftProtobuf._MessageImplementationBase, SwiftProtobuf._ProtoNameProviding {
  static let protoMessageName: String = _protobuf_package + ".UT_IPConfigMessage"
  static let _protobuf_nameMap: SwiftProtobuf._NameMap = [
    1: .standard(proto: "ip_address"),
    2: .standard(proto: "dest_address"),
    3: .same(proto: "netmask"),
    4: .same(proto: "mtu"),
    5: .standard(proto: "bring_up"),
  ]

  mutating func decodeMessage<D: SwiftProtobuf.Decoder>(decoder: inout D) throws {
    while let fieldNumber = try decoder.nextFieldNumber() {
      // The use of inline closures is to circumvent an issue where the compiler
      // allocates stack space for every case branch when no optimizations are
      // enabled. https://github.com/apple/swift-protobuf/issues/1034
      switch fieldNumber {
      case 1: try { try decoder.decodeSingularStringField(value: &self.ipAddress) }()
      case 2: try { try decoder.decodeSingularStringField(value: &self.destAddress) }()
      case 3: try { try decoder.decodeSingularStringField(value: &self.netmask) }()
      case 4: try { try decoder.decodeSingularInt32Field(value: &self.mtu) }()
      case 5: try { try decoder.decodeSingularBoolField(value: &self.bringUp) }()
      default: break
      }
    }
  }

  func traverse<V: SwiftProtobuf.Visitor>(visitor: inout V) throws {
    if !self.ipAddress.isEmpty {
      try visitor.visitSingularStringField(value: self.ipAddress, fieldNumber: 1)
    }
    if !self.destAddress.isEmpty {
      try visitor.visitSingularStringField(value: self.destAddress, fieldNumber: 2)
    }
    if !self.netmask.isEmpty {
      try visitor.visitSingularStringField(value: self.netmask, fieldNumber: 3)
    }
    if self.mtu != 0 {
      try visitor.visitSingularInt32Field(value: self.mtu, fieldNumber: 4)
    }
    if self.bringUp != false {
      try visitor.visitSingularBoolField(value: self.bringUp, fieldNumber: 5)
    }
    try unknownFields.traverse(visitor: &visitor)
  }

  static func ==(lhs: IAnts_UT_IPConfigMessage, rhs: IAnts_UT_IPConfigMessage) -> Bool {
    if lhs.ipAddress != rhs.ipAddress {return false}
    if lhs.destAddress != rhs.destAddress {return false}
    if lhs.netmask != rhs.netmask {return false}
    if lhs.mtu != rhs.mtu {return false}
    if lhs.bringUp != rhs.bringUp {return false}
    if lhs.unknownFields != rhs.unknownFields {return false}
    return true
  }
}

extension IAnts_UT_ProxyConfigMessage: SwiftProtobuf.Message, SwiftProtobuf._MessageImplementationBase, SwiftProtobuf._ProtoNameProviding {
  static let protoMessageName: String = _protobuf_package + ".UT_ProxyConfigMessage"
  static let _protobuf_nameMap: SwiftProtobuf._NameMap = [
    1: .same(proto: "type"),
    2: .same(proto: "host"),
    3: .same(proto: "port"),
    4: .same(proto: "username"),
    5: .same(proto: "password"),
    6: .standard(proto: "extra_config"),
  ]

  mutating func decodeMessage<D: SwiftProtobuf.Decoder>(decoder: inout D) throws {
    while let fieldNumber = try decoder.nextFieldNumber() {
      // The use of inline closures is to circumvent an issue where the compiler
      // allocates stack space for every case branch when no optimizations are
      // enabled. https://github.com/apple/swift-protobuf/issues/1034
      switch fieldNumber {
      case 1: try { try decoder.decodeSingularEnumField(value: &self.type) }()
      case 2: try { try decoder.decodeSingularStringField(value: &self.host) }()
      case 3: try { try decoder.decodeSingularInt32Field(value: &self.port) }()
      case 4: try { try decoder.decodeSingularStringField(value: &self.username) }()
      case 5: try { try decoder.decodeSingularStringField(value: &self.password) }()
      case 6: try { try decoder.decodeSingularStringField(value: &self.extraConfig) }()
      default: break
      }
    }
  }

  func traverse<V: SwiftProtobuf.Visitor>(visitor: inout V) throws {
    if self.type != .http {
      try visitor.visitSingularEnumField(value: self.type, fieldNumber: 1)
    }
    if !self.host.isEmpty {
      try visitor.visitSingularStringField(value: self.host, fieldNumber: 2)
    }
    if self.port != 0 {
      try visitor.visitSingularInt32Field(value: self.port, fieldNumber: 3)
    }
    if !self.username.isEmpty {
      try visitor.visitSingularStringField(value: self.username, fieldNumber: 4)
    }
    if !self.password.isEmpty {
      try visitor.visitSingularStringField(value: self.password, fieldNumber: 5)
    }
    if !self.extraConfig.isEmpty {
      try visitor.visitSingularStringField(value: self.extraConfig, fieldNumber: 6)
    }
    try unknownFields.traverse(visitor: &visitor)
  }

  static func ==(lhs: IAnts_UT_ProxyConfigMessage, rhs: IAnts_UT_ProxyConfigMessage) -> Bool {
    if lhs.type != rhs.type {return false}
    if lhs.host != rhs.host {return false}
    if lhs.port != rhs.port {return false}
    if lhs.username != rhs.username {return false}
    if lhs.password != rhs.password {return false}
    if lhs.extraConfig != rhs.extraConfig {return false}
    if lhs.unknownFields != rhs.unknownFields {return false}
    return true
  }
}

extension IAnts_UT_ProxyConfigMessage.ProxyType: SwiftProtobuf._ProtoNameProviding {
  static let _protobuf_nameMap: SwiftProtobuf._NameMap = [
    0: .same(proto: "HTTP"),
    1: .same(proto: "HTTPS"),
    2: .same(proto: "SOCKS5"),
    3: .same(proto: "SHADOWSOCKS"),
  ]
}

extension IAnts_UT_ProxyActionMessage: SwiftProtobuf.Message, SwiftProtobuf._MessageImplementationBase, SwiftProtobuf._ProtoNameProviding {
  static let protoMessageName: String = _protobuf_package + ".UT_ProxyActionMessage"
  static let _protobuf_nameMap: SwiftProtobuf._NameMap = [
    1: .same(proto: "action"),
  ]

  mutating func decodeMessage<D: SwiftProtobuf.Decoder>(decoder: inout D) throws {
    while let fieldNumber = try decoder.nextFieldNumber() {
      // The use of inline closures is to circumvent an issue where the compiler
      // allocates stack space for every case branch when no optimizations are
      // enabled. https://github.com/apple/swift-protobuf/issues/1034
      switch fieldNumber {
      case 1: try { try decoder.decodeSingularEnumField(value: &self.action) }()
      default: break
      }
    }
  }

  func traverse<V: SwiftProtobuf.Visitor>(visitor: inout V) throws {
    if self.action != .start {
      try visitor.visitSingularEnumField(value: self.action, fieldNumber: 1)
    }
    try unknownFields.traverse(visitor: &visitor)
  }

  static func ==(lhs: IAnts_UT_ProxyActionMessage, rhs: IAnts_UT_ProxyActionMessage) -> Bool {
    if lhs.action != rhs.action {return false}
    if lhs.unknownFields != rhs.unknownFields {return false}
    return true
  }
}

extension IAnts_UT_ProxyActionMessage.Action: SwiftProtobuf._ProtoNameProviding {
  static let _protobuf_nameMap: SwiftProtobuf._NameMap = [
    0: .same(proto: "START"),
    1: .same(proto: "STOP"),
    2: .same(proto: "RESTART"),
  ]
}

extension IAnts_UT_ProxyStatusMessage: SwiftProtobuf.Message, SwiftProtobuf._MessageImplementationBase, SwiftProtobuf._ProtoNameProviding {
  static let protoMessageName: String = _protobuf_package + ".UT_ProxyStatusMessage"
  static let _protobuf_nameMap: SwiftProtobuf._NameMap = [
    1: .same(proto: "running"),
    2: .standard(proto: "proxy_url"),
    3: .standard(proto: "bytes_sent"),
    4: .standard(proto: "bytes_received"),
    5: .standard(proto: "start_time"),
  ]

  mutating func decodeMessage<D: SwiftProtobuf.Decoder>(decoder: inout D) throws {
    while let fieldNumber = try decoder.nextFieldNumber() {
      // The use of inline closures is to circumvent an issue where the compiler
      // allocates stack space for every case branch when no optimizations are
      // enabled. https://github.com/apple/swift-protobuf/issues/1034
      switch fieldNumber {
      case 1: try { try decoder.decodeSingularBoolField(value: &self.running) }()
      case 2: try { try decoder.decodeSingularStringField(value: &self.proxyURL) }()
      case 3: try { try decoder.decodeSingularInt64Field(value: &self.bytesSent) }()
      case 4: try { try decoder.decodeSingularInt64Field(value: &self.bytesReceived) }()
      case 5: try { try decoder.decodeSingularInt64Field(value: &self.startTime) }()
      default: break
      }
    }
  }

  func traverse<V: SwiftProtobuf.Visitor>(visitor: inout V) throws {
    if self.running != false {
      try visitor.visitSingularBoolField(value: self.running, fieldNumber: 1)
    }
    if !self.proxyURL.isEmpty {
      try visitor.visitSingularStringField(value: self.proxyURL, fieldNumber: 2)
    }
    if self.bytesSent != 0 {
      try visitor.visitSingularInt64Field(value: self.bytesSent, fieldNumber: 3)
    }
    if self.bytesReceived != 0 {
      try visitor.visitSingularInt64Field(value: self.bytesReceived, fieldNumber: 4)
    }
    if self.startTime != 0 {
      try visitor.visitSingularInt64Field(value: self.startTime, fieldNumber: 5)
    }
    try unknownFields.traverse(visitor: &visitor)
  }

  static func ==(lhs: IAnts_UT_ProxyStatusMessage, rhs: IAnts_UT_ProxyStatusMessage) -> Bool {
    if lhs.running != rhs.running {return false}
    if lhs.proxyURL != rhs.proxyURL {return false}
    if lhs.bytesSent != rhs.bytesSent {return false}
    if lhs.bytesReceived != rhs.bytesReceived {return false}
    if lhs.startTime != rhs.startTime {return false}
    if lhs.unknownFields != rhs.unknownFields {return false}
    return true
  }
}

extension IAnts_UT_DefaultMessage: SwiftProtobuf.Message, SwiftProtobuf._MessageImplementationBase, SwiftProtobuf._ProtoNameProviding {
  static let protoMessageName: String = _protobuf_package + ".UT_DefaultMessage"
  static let _protobuf_nameMap = SwiftProtobuf._NameMap()

  mutating func decodeMessage<D: SwiftProtobuf.Decoder>(decoder: inout D) throws {
    // Load everything into unknown fields
    while try decoder.nextFieldNumber() != nil {}
  }

  func traverse<V: SwiftProtobuf.Visitor>(visitor: inout V) throws {
    try unknownFields.traverse(visitor: &visitor)
  }

  static func ==(lhs: IAnts_UT_DefaultMessage, rhs: IAnts_UT_DefaultMessage) -> Bool {
    if lhs.unknownFields != rhs.unknownFields {return false}
    return true
  }
}

extension IAnts_FilterConfig: SwiftProtobuf.Message, SwiftProtobuf._MessageImplementationBase, SwiftProtobuf._ProtoNameProviding {
  static let protoMessageName: String = _protobuf_package + ".FilterConfig"
  static let _protobuf_nameMap: SwiftProtobuf._NameMap = [
    1: .standard(proto: "allowed_domains"),
    2: .standard(proto: "blocked_domains"),
    3: .standard(proto: "allowed_ips"),
    4: .standard(proto: "blocked_ips"),
  ]

  mutating func decodeMessage<D: SwiftProtobuf.Decoder>(decoder: inout D) throws {
    while let fieldNumber = try decoder.nextFieldNumber() {
      // The use of inline closures is to circumvent an issue where the compiler
      // allocates stack space for every case branch when no optimizations are
      // enabled. https://github.com/apple/swift-protobuf/issues/1034
      switch fieldNumber {
      case 1: try { try decoder.decodeRepeatedStringField(value: &self.allowedDomains) }()
      case 2: try { try decoder.decodeRepeatedStringField(value: &self.blockedDomains) }()
      case 3: try { try decoder.decodeRepeatedStringField(value: &self.allowedIps) }()
      case 4: try { try decoder.decodeRepeatedStringField(value: &self.blockedIps) }()
      default: break
      }
    }
  }

  func traverse<V: SwiftProtobuf.Visitor>(visitor: inout V) throws {
    if !self.allowedDomains.isEmpty {
      try visitor.visitRepeatedStringField(value: self.allowedDomains, fieldNumber: 1)
    }
    if !self.blockedDomains.isEmpty {
      try visitor.visitRepeatedStringField(value: self.blockedDomains, fieldNumber: 2)
    }
    if !self.allowedIps.isEmpty {
      try visitor.visitRepeatedStringField(value: self.allowedIps, fieldNumber: 3)
    }
    if !self.blockedIps.isEmpty {
      try visitor.visitRepeatedStringField(value: self.blockedIps, fieldNumber: 4)
    }
    try unknownFields.traverse(visitor: &visitor)
  }

  static func ==(lhs: IAnts_FilterConfig, rhs: IAnts_FilterConfig) -> Bool {
    if lhs.allowedDomains != rhs.allowedDomains {return false}
    if lhs.blockedDomains != rhs.blockedDomains {return false}
    if lhs.allowedIps != rhs.allowedIps {return false}
    if lhs.blockedIps != rhs.blockedIps {return false}
    if lhs.unknownFields != rhs.unknownFields {return false}
    return true
  }
}

extension IAnts_UT_StartPureProxyMessage: SwiftProtobuf.Message, SwiftProtobuf._MessageImplementationBase, SwiftProtobuf._ProtoNameProviding {
  static let protoMessageName: String = _protobuf_package + ".UT_StartPureProxyMessage"
  static let _protobuf_nameMap: SwiftProtobuf._NameMap = [
    1: .standard(proto: "proxy_url"),
    2: .standard(proto: "enable_udp"),
    3: .standard(proto: "global_proxy"),
  ]

  mutating func decodeMessage<D: SwiftProtobuf.Decoder>(decoder: inout D) throws {
    while let fieldNumber = try decoder.nextFieldNumber() {
      // The use of inline closures is to circumvent an issue where the compiler
      // allocates stack space for every case branch when no optimizations are
      // enabled. https://github.com/apple/swift-protobuf/issues/1034
      switch fieldNumber {
      case 1: try { try decoder.decodeSingularStringField(value: &self.proxyURL) }()
      case 2: try { try decoder.decodeSingularBoolField(value: &self.enableUdp) }()
      case 3: try { try decoder.decodeSingularBoolField(value: &self.globalProxy) }()
      default: break
      }
    }
  }

  func traverse<V: SwiftProtobuf.Visitor>(visitor: inout V) throws {
    if !self.proxyURL.isEmpty {
      try visitor.visitSingularStringField(value: self.proxyURL, fieldNumber: 1)
    }
    if self.enableUdp != false {
      try visitor.visitSingularBoolField(value: self.enableUdp, fieldNumber: 2)
    }
    if self.globalProxy != false {
      try visitor.visitSingularBoolField(value: self.globalProxy, fieldNumber: 3)
    }
    try unknownFields.traverse(visitor: &visitor)
  }

  static func ==(lhs: IAnts_UT_StartPureProxyMessage, rhs: IAnts_UT_StartPureProxyMessage) -> Bool {
    if lhs.proxyURL != rhs.proxyURL {return false}
    if lhs.enableUdp != rhs.enableUdp {return false}
    if lhs.globalProxy != rhs.globalProxy {return false}
    if lhs.unknownFields != rhs.unknownFields {return false}
    return true
  }
}

extension IAnts_UT_StartPureCaptureMessage: SwiftProtobuf.Message, SwiftProtobuf._MessageImplementationBase, SwiftProtobuf._ProtoNameProviding {
  static let protoMessageName: String = _protobuf_package + ".UT_StartPureCaptureMessage"
  static let _protobuf_nameMap: SwiftProtobuf._NameMap = [
    1: .standard(proto: "filter_config"),
    2: .standard(proto: "remote_ws_server"),
    3: .standard(proto: "enable_udp"),
    4: .standard(proto: "global_proxy"),
  ]

  mutating func decodeMessage<D: SwiftProtobuf.Decoder>(decoder: inout D) throws {
    while let fieldNumber = try decoder.nextFieldNumber() {
      // The use of inline closures is to circumvent an issue where the compiler
      // allocates stack space for every case branch when no optimizations are
      // enabled. https://github.com/apple/swift-protobuf/issues/1034
      switch fieldNumber {
      case 1: try { try decoder.decodeSingularMessageField(value: &self._filterConfig) }()
      case 2: try { try decoder.decodeSingularStringField(value: &self.remoteWsServer) }()
      case 3: try { try decoder.decodeSingularBoolField(value: &self.enableUdp) }()
      case 4: try { try decoder.decodeSingularBoolField(value: &self.globalProxy) }()
      default: break
      }
    }
  }

  func traverse<V: SwiftProtobuf.Visitor>(visitor: inout V) throws {
    // The use of inline closures is to circumvent an issue where the compiler
    // allocates stack space for every if/case branch local when no optimizations
    // are enabled. https://github.com/apple/swift-protobuf/issues/1034 and
    // https://github.com/apple/swift-protobuf/issues/1182
    try { if let v = self._filterConfig {
      try visitor.visitSingularMessageField(value: v, fieldNumber: 1)
    } }()
    if !self.remoteWsServer.isEmpty {
      try visitor.visitSingularStringField(value: self.remoteWsServer, fieldNumber: 2)
    }
    if self.enableUdp != false {
      try visitor.visitSingularBoolField(value: self.enableUdp, fieldNumber: 3)
    }
    if self.globalProxy != false {
      try visitor.visitSingularBoolField(value: self.globalProxy, fieldNumber: 4)
    }
    try unknownFields.traverse(visitor: &visitor)
  }

  static func ==(lhs: IAnts_UT_StartPureCaptureMessage, rhs: IAnts_UT_StartPureCaptureMessage) -> Bool {
    if lhs._filterConfig != rhs._filterConfig {return false}
    if lhs.remoteWsServer != rhs.remoteWsServer {return false}
    if lhs.enableUdp != rhs.enableUdp {return false}
    if lhs.globalProxy != rhs.globalProxy {return false}
    if lhs.unknownFields != rhs.unknownFields {return false}
    return true
  }
}

extension IAnts_UT_StartProxyCaptureMessage: SwiftProtobuf.Message, SwiftProtobuf._MessageImplementationBase, SwiftProtobuf._ProtoNameProviding {
  static let protoMessageName: String = _protobuf_package + ".UT_StartProxyCaptureMessage"
  static let _protobuf_nameMap: SwiftProtobuf._NameMap = [
    1: .standard(proto: "filter_config"),
    2: .standard(proto: "proxy_url"),
    3: .standard(proto: "enable_udp"),
    4: .standard(proto: "global_proxy"),
    5: .standard(proto: "remote_ws_server"),
  ]

  mutating func decodeMessage<D: SwiftProtobuf.Decoder>(decoder: inout D) throws {
    while let fieldNumber = try decoder.nextFieldNumber() {
      // The use of inline closures is to circumvent an issue where the compiler
      // allocates stack space for every case branch when no optimizations are
      // enabled. https://github.com/apple/swift-protobuf/issues/1034
      switch fieldNumber {
      case 1: try { try decoder.decodeSingularMessageField(value: &self._filterConfig) }()
      case 2: try { try decoder.decodeSingularStringField(value: &self.proxyURL) }()
      case 3: try { try decoder.decodeSingularBoolField(value: &self.enableUdp) }()
      case 4: try { try decoder.decodeSingularBoolField(value: &self.globalProxy) }()
      case 5: try { try decoder.decodeSingularStringField(value: &self.remoteWsServer) }()
      default: break
      }
    }
  }

  func traverse<V: SwiftProtobuf.Visitor>(visitor: inout V) throws {
    // The use of inline closures is to circumvent an issue where the compiler
    // allocates stack space for every if/case branch local when no optimizations
    // are enabled. https://github.com/apple/swift-protobuf/issues/1034 and
    // https://github.com/apple/swift-protobuf/issues/1182
    try { if let v = self._filterConfig {
      try visitor.visitSingularMessageField(value: v, fieldNumber: 1)
    } }()
    if !self.proxyURL.isEmpty {
      try visitor.visitSingularStringField(value: self.proxyURL, fieldNumber: 2)
    }
    if self.enableUdp != false {
      try visitor.visitSingularBoolField(value: self.enableUdp, fieldNumber: 3)
    }
    if self.globalProxy != false {
      try visitor.visitSingularBoolField(value: self.globalProxy, fieldNumber: 4)
    }
    if !self.remoteWsServer.isEmpty {
      try visitor.visitSingularStringField(value: self.remoteWsServer, fieldNumber: 5)
    }
    try unknownFields.traverse(visitor: &visitor)
  }

  static func ==(lhs: IAnts_UT_StartProxyCaptureMessage, rhs: IAnts_UT_StartProxyCaptureMessage) -> Bool {
    if lhs._filterConfig != rhs._filterConfig {return false}
    if lhs.proxyURL != rhs.proxyURL {return false}
    if lhs.enableUdp != rhs.enableUdp {return false}
    if lhs.globalProxy != rhs.globalProxy {return false}
    if lhs.remoteWsServer != rhs.remoteWsServer {return false}
    if lhs.unknownFields != rhs.unknownFields {return false}
    return true
  }
}

extension IAnts_UT_StopModeMessage: SwiftProtobuf.Message, SwiftProtobuf._MessageImplementationBase, SwiftProtobuf._ProtoNameProviding {
  static let protoMessageName: String = _protobuf_package + ".UT_StopModeMessage"
  static let _protobuf_nameMap = SwiftProtobuf._NameMap()

  mutating func decodeMessage<D: SwiftProtobuf.Decoder>(decoder: inout D) throws {
    // Load everything into unknown fields
    while try decoder.nextFieldNumber() != nil {}
  }

  func traverse<V: SwiftProtobuf.Visitor>(visitor: inout V) throws {
    try unknownFields.traverse(visitor: &visitor)
  }

  static func ==(lhs: IAnts_UT_StopModeMessage, rhs: IAnts_UT_StopModeMessage) -> Bool {
    if lhs.unknownFields != rhs.unknownFields {return false}
    return true
  }
}

extension IAnts_UT_ModeStatusMessage: SwiftProtobuf.Message, SwiftProtobuf._MessageImplementationBase, SwiftProtobuf._ProtoNameProviding {
  static let protoMessageName: String = _protobuf_package + ".UT_ModeStatusMessage"
  static let _protobuf_nameMap: SwiftProtobuf._NameMap = [
    1: .same(proto: "state"),
    2: .same(proto: "mode"),
    3: .standard(proto: "error_message"),
    4: .standard(proto: "start_time"),
    5: .standard(proto: "proxy_url"),
    6: .standard(proto: "remote_ws_server"),
    7: .standard(proto: "enable_udp"),
    8: .standard(proto: "global_proxy"),
  ]

  mutating func decodeMessage<D: SwiftProtobuf.Decoder>(decoder: inout D) throws {
    while let fieldNumber = try decoder.nextFieldNumber() {
      // The use of inline closures is to circumvent an issue where the compiler
      // allocates stack space for every case branch when no optimizations are
      // enabled. https://github.com/apple/swift-protobuf/issues/1034
      switch fieldNumber {
      case 1: try { try decoder.decodeSingularEnumField(value: &self.state) }()
      case 2: try { try decoder.decodeSingularEnumField(value: &self.mode) }()
      case 3: try { try decoder.decodeSingularStringField(value: &self.errorMessage) }()
      case 4: try { try decoder.decodeSingularInt64Field(value: &self.startTime) }()
      case 5: try { try decoder.decodeSingularStringField(value: &self.proxyURL) }()
      case 6: try { try decoder.decodeSingularStringField(value: &self.remoteWsServer) }()
      case 7: try { try decoder.decodeSingularBoolField(value: &self.enableUdp) }()
      case 8: try { try decoder.decodeSingularBoolField(value: &self.globalProxy) }()
      default: break
      }
    }
  }

  func traverse<V: SwiftProtobuf.Visitor>(visitor: inout V) throws {
    if self.state != .modeStopped {
      try visitor.visitSingularEnumField(value: self.state, fieldNumber: 1)
    }
    if self.mode != .pureProxy {
      try visitor.visitSingularEnumField(value: self.mode, fieldNumber: 2)
    }
    if !self.errorMessage.isEmpty {
      try visitor.visitSingularStringField(value: self.errorMessage, fieldNumber: 3)
    }
    if self.startTime != 0 {
      try visitor.visitSingularInt64Field(value: self.startTime, fieldNumber: 4)
    }
    if !self.proxyURL.isEmpty {
      try visitor.visitSingularStringField(value: self.proxyURL, fieldNumber: 5)
    }
    if !self.remoteWsServer.isEmpty {
      try visitor.visitSingularStringField(value: self.remoteWsServer, fieldNumber: 6)
    }
    if self.enableUdp != false {
      try visitor.visitSingularBoolField(value: self.enableUdp, fieldNumber: 7)
    }
    if self.globalProxy != false {
      try visitor.visitSingularBoolField(value: self.globalProxy, fieldNumber: 8)
    }
    try unknownFields.traverse(visitor: &visitor)
  }

  static func ==(lhs: IAnts_UT_ModeStatusMessage, rhs: IAnts_UT_ModeStatusMessage) -> Bool {
    if lhs.state != rhs.state {return false}
    if lhs.mode != rhs.mode {return false}
    if lhs.errorMessage != rhs.errorMessage {return false}
    if lhs.startTime != rhs.startTime {return false}
    if lhs.proxyURL != rhs.proxyURL {return false}
    if lhs.remoteWsServer != rhs.remoteWsServer {return false}
    if lhs.enableUdp != rhs.enableUdp {return false}
    if lhs.globalProxy != rhs.globalProxy {return false}
    if lhs.unknownFields != rhs.unknownFields {return false}
    return true
  }
}

extension IAnts_UT_SetRouteWhitelistMessage: SwiftProtobuf.Message, SwiftProtobuf._MessageImplementationBase, SwiftProtobuf._ProtoNameProviding {
  static let protoMessageName: String = _protobuf_package + ".UT_SetRouteWhitelistMessage"
  static let _protobuf_nameMap: SwiftProtobuf._NameMap = [
    1: .same(proto: "whitelist"),
  ]

  mutating func decodeMessage<D: SwiftProtobuf.Decoder>(decoder: inout D) throws {
    while let fieldNumber = try decoder.nextFieldNumber() {
      // The use of inline closures is to circumvent an issue where the compiler
      // allocates stack space for every case branch when no optimizations are
      // enabled. https://github.com/apple/swift-protobuf/issues/1034
      switch fieldNumber {
      case 1: try { try decoder.decodeRepeatedStringField(value: &self.whitelist) }()
      default: break
      }
    }
  }

  func traverse<V: SwiftProtobuf.Visitor>(visitor: inout V) throws {
    if !self.whitelist.isEmpty {
      try visitor.visitRepeatedStringField(value: self.whitelist, fieldNumber: 1)
    }
    try unknownFields.traverse(visitor: &visitor)
  }

  static func ==(lhs: IAnts_UT_SetRouteWhitelistMessage, rhs: IAnts_UT_SetRouteWhitelistMessage) -> Bool {
    if lhs.whitelist != rhs.whitelist {return false}
    if lhs.unknownFields != rhs.unknownFields {return false}
    return true
  }
}

extension IAnts_UT_SetRouteBlacklistMessage: SwiftProtobuf.Message, SwiftProtobuf._MessageImplementationBase, SwiftProtobuf._ProtoNameProviding {
  static let protoMessageName: String = _protobuf_package + ".UT_SetRouteBlacklistMessage"
  static let _protobuf_nameMap: SwiftProtobuf._NameMap = [
    1: .same(proto: "blacklist"),
  ]

  mutating func decodeMessage<D: SwiftProtobuf.Decoder>(decoder: inout D) throws {
    while let fieldNumber = try decoder.nextFieldNumber() {
      // The use of inline closures is to circumvent an issue where the compiler
      // allocates stack space for every case branch when no optimizations are
      // enabled. https://github.com/apple/swift-protobuf/issues/1034
      switch fieldNumber {
      case 1: try { try decoder.decodeRepeatedStringField(value: &self.blacklist) }()
      default: break
      }
    }
  }

  func traverse<V: SwiftProtobuf.Visitor>(visitor: inout V) throws {
    if !self.blacklist.isEmpty {
      try visitor.visitRepeatedStringField(value: self.blacklist, fieldNumber: 1)
    }
    try unknownFields.traverse(visitor: &visitor)
  }

  static func ==(lhs: IAnts_UT_SetRouteBlacklistMessage, rhs: IAnts_UT_SetRouteBlacklistMessage) -> Bool {
    if lhs.blacklist != rhs.blacklist {return false}
    if lhs.unknownFields != rhs.unknownFields {return false}
    return true
  }
}

extension IAnts_UT_SetProxyWhitelistMessage: SwiftProtobuf.Message, SwiftProtobuf._MessageImplementationBase, SwiftProtobuf._ProtoNameProviding {
  static let protoMessageName: String = _protobuf_package + ".UT_SetProxyWhitelistMessage"
  static let _protobuf_nameMap: SwiftProtobuf._NameMap = [
    1: .same(proto: "whitelist"),
  ]

  mutating func decodeMessage<D: SwiftProtobuf.Decoder>(decoder: inout D) throws {
    while let fieldNumber = try decoder.nextFieldNumber() {
      // The use of inline closures is to circumvent an issue where the compiler
      // allocates stack space for every case branch when no optimizations are
      // enabled. https://github.com/apple/swift-protobuf/issues/1034
      switch fieldNumber {
      case 1: try { try decoder.decodeRepeatedStringField(value: &self.whitelist) }()
      default: break
      }
    }
  }

  func traverse<V: SwiftProtobuf.Visitor>(visitor: inout V) throws {
    if !self.whitelist.isEmpty {
      try visitor.visitRepeatedStringField(value: self.whitelist, fieldNumber: 1)
    }
    try unknownFields.traverse(visitor: &visitor)
  }

  static func ==(lhs: IAnts_UT_SetProxyWhitelistMessage, rhs: IAnts_UT_SetProxyWhitelistMessage) -> Bool {
    if lhs.whitelist != rhs.whitelist {return false}
    if lhs.unknownFields != rhs.unknownFields {return false}
    return true
  }
}

extension IAnts_UT_SetProxyBlacklistMessage: SwiftProtobuf.Message, SwiftProtobuf._MessageImplementationBase, SwiftProtobuf._ProtoNameProviding {
  static let protoMessageName: String = _protobuf_package + ".UT_SetProxyBlacklistMessage"
  static let _protobuf_nameMap: SwiftProtobuf._NameMap = [
    1: .same(proto: "blacklist"),
  ]

  mutating func decodeMessage<D: SwiftProtobuf.Decoder>(decoder: inout D) throws {
    while let fieldNumber = try decoder.nextFieldNumber() {
      // The use of inline closures is to circumvent an issue where the compiler
      // allocates stack space for every case branch when no optimizations are
      // enabled. https://github.com/apple/swift-protobuf/issues/1034
      switch fieldNumber {
      case 1: try { try decoder.decodeRepeatedStringField(value: &self.blacklist) }()
      default: break
      }
    }
  }

  func traverse<V: SwiftProtobuf.Visitor>(visitor: inout V) throws {
    if !self.blacklist.isEmpty {
      try visitor.visitRepeatedStringField(value: self.blacklist, fieldNumber: 1)
    }
    try unknownFields.traverse(visitor: &visitor)
  }

  static func ==(lhs: IAnts_UT_SetProxyBlacklistMessage, rhs: IAnts_UT_SetProxyBlacklistMessage) -> Bool {
    if lhs.blacklist != rhs.blacklist {return false}
    if lhs.unknownFields != rhs.unknownFields {return false}
    return true
  }
}

extension IAnts_UT_GetAllConfigMessage: SwiftProtobuf.Message, SwiftProtobuf._MessageImplementationBase, SwiftProtobuf._ProtoNameProviding {
  static let protoMessageName: String = _protobuf_package + ".UT_GetAllConfigMessage"
  static let _protobuf_nameMap = SwiftProtobuf._NameMap()

  mutating func decodeMessage<D: SwiftProtobuf.Decoder>(decoder: inout D) throws {
    // Load everything into unknown fields
    while try decoder.nextFieldNumber() != nil {}
  }

  func traverse<V: SwiftProtobuf.Visitor>(visitor: inout V) throws {
    try unknownFields.traverse(visitor: &visitor)
  }

  static func ==(lhs: IAnts_UT_GetAllConfigMessage, rhs: IAnts_UT_GetAllConfigMessage) -> Bool {
    if lhs.unknownFields != rhs.unknownFields {return false}
    return true
  }
}

extension IAnts_UT_AllConfigResponseMessage: SwiftProtobuf.Message, SwiftProtobuf._MessageImplementationBase, SwiftProtobuf._ProtoNameProviding {
  static let protoMessageName: String = _protobuf_package + ".UT_AllConfigResponseMessage"
  static let _protobuf_nameMap: SwiftProtobuf._NameMap = [
    1: .standard(proto: "route_whitelist"),
    2: .standard(proto: "route_blacklist"),
    3: .standard(proto: "proxy_whitelist"),
    4: .standard(proto: "proxy_blacklist"),
  ]

  mutating func decodeMessage<D: SwiftProtobuf.Decoder>(decoder: inout D) throws {
    while let fieldNumber = try decoder.nextFieldNumber() {
      // The use of inline closures is to circumvent an issue where the compiler
      // allocates stack space for every case branch when no optimizations are
      // enabled. https://github.com/apple/swift-protobuf/issues/1034
      switch fieldNumber {
      case 1: try { try decoder.decodeRepeatedStringField(value: &self.routeWhitelist) }()
      case 2: try { try decoder.decodeRepeatedStringField(value: &self.routeBlacklist) }()
      case 3: try { try decoder.decodeRepeatedStringField(value: &self.proxyWhitelist) }()
      case 4: try { try decoder.decodeRepeatedStringField(value: &self.proxyBlacklist) }()
      default: break
      }
    }
  }

  func traverse<V: SwiftProtobuf.Visitor>(visitor: inout V) throws {
    if !self.routeWhitelist.isEmpty {
      try visitor.visitRepeatedStringField(value: self.routeWhitelist, fieldNumber: 1)
    }
    if !self.routeBlacklist.isEmpty {
      try visitor.visitRepeatedStringField(value: self.routeBlacklist, fieldNumber: 2)
    }
    if !self.proxyWhitelist.isEmpty {
      try visitor.visitRepeatedStringField(value: self.proxyWhitelist, fieldNumber: 3)
    }
    if !self.proxyBlacklist.isEmpty {
      try visitor.visitRepeatedStringField(value: self.proxyBlacklist, fieldNumber: 4)
    }
    try unknownFields.traverse(visitor: &visitor)
  }

  static func ==(lhs: IAnts_UT_AllConfigResponseMessage, rhs: IAnts_UT_AllConfigResponseMessage) -> Bool {
    if lhs.routeWhitelist != rhs.routeWhitelist {return false}
    if lhs.routeBlacklist != rhs.routeBlacklist {return false}
    if lhs.proxyWhitelist != rhs.proxyWhitelist {return false}
    if lhs.proxyBlacklist != rhs.proxyBlacklist {return false}
    if lhs.unknownFields != rhs.unknownFields {return false}
    return true
  }
}

extension IAnts_UT_GetRoutingTableMessage: SwiftProtobuf.Message, SwiftProtobuf._MessageImplementationBase, SwiftProtobuf._ProtoNameProviding {
  static let protoMessageName: String = _protobuf_package + ".UT_GetRoutingTableMessage"
  static let _protobuf_nameMap = SwiftProtobuf._NameMap()

  mutating func decodeMessage<D: SwiftProtobuf.Decoder>(decoder: inout D) throws {
    // Load everything into unknown fields
    while try decoder.nextFieldNumber() != nil {}
  }

  func traverse<V: SwiftProtobuf.Visitor>(visitor: inout V) throws {
    try unknownFields.traverse(visitor: &visitor)
  }

  static func ==(lhs: IAnts_UT_GetRoutingTableMessage, rhs: IAnts_UT_GetRoutingTableMessage) -> Bool {
    if lhs.unknownFields != rhs.unknownFields {return false}
    return true
  }
}

extension IAnts_RouteEntry: SwiftProtobuf.Message, SwiftProtobuf._MessageImplementationBase, SwiftProtobuf._ProtoNameProviding {
  static let protoMessageName: String = _protobuf_package + ".RouteEntry"
  static let _protobuf_nameMap: SwiftProtobuf._NameMap = [
    1: .same(proto: "destination"),
    2: .same(proto: "gateway"),
    3: .same(proto: "netmask"),
    4: .same(proto: "interface"),
    5: .same(proto: "flags"),
    6: .same(proto: "metric"),
    7: .standard(proto: "is_ipv6"),
    8: .standard(proto: "has_mac"),
    9: .standard(proto: "mac_address"),
  ]

  mutating func decodeMessage<D: SwiftProtobuf.Decoder>(decoder: inout D) throws {
    while let fieldNumber = try decoder.nextFieldNumber() {
      // The use of inline closures is to circumvent an issue where the compiler
      // allocates stack space for every case branch when no optimizations are
      // enabled. https://github.com/apple/swift-protobuf/issues/1034
      switch fieldNumber {
      case 1: try { try decoder.decodeSingularStringField(value: &self.destination) }()
      case 2: try { try decoder.decodeSingularStringField(value: &self.gateway) }()
      case 3: try { try decoder.decodeSingularStringField(value: &self.netmask) }()
      case 4: try { try decoder.decodeSingularStringField(value: &self.interface) }()
      case 5: try { try decoder.decodeSingularInt32Field(value: &self.flags) }()
      case 6: try { try decoder.decodeSingularInt32Field(value: &self.metric) }()
      case 7: try { try decoder.decodeSingularBoolField(value: &self.isIpv6) }()
      case 8: try { try decoder.decodeSingularBoolField(value: &self.hasMac_p) }()
      case 9: try { try decoder.decodeSingularStringField(value: &self.macAddress) }()
      default: break
      }
    }
  }

  func traverse<V: SwiftProtobuf.Visitor>(visitor: inout V) throws {
    if !self.destination.isEmpty {
      try visitor.visitSingularStringField(value: self.destination, fieldNumber: 1)
    }
    if !self.gateway.isEmpty {
      try visitor.visitSingularStringField(value: self.gateway, fieldNumber: 2)
    }
    if !self.netmask.isEmpty {
      try visitor.visitSingularStringField(value: self.netmask, fieldNumber: 3)
    }
    if !self.interface.isEmpty {
      try visitor.visitSingularStringField(value: self.interface, fieldNumber: 4)
    }
    if self.flags != 0 {
      try visitor.visitSingularInt32Field(value: self.flags, fieldNumber: 5)
    }
    if self.metric != 0 {
      try visitor.visitSingularInt32Field(value: self.metric, fieldNumber: 6)
    }
    if self.isIpv6 != false {
      try visitor.visitSingularBoolField(value: self.isIpv6, fieldNumber: 7)
    }
    if self.hasMac_p != false {
      try visitor.visitSingularBoolField(value: self.hasMac_p, fieldNumber: 8)
    }
    if !self.macAddress.isEmpty {
      try visitor.visitSingularStringField(value: self.macAddress, fieldNumber: 9)
    }
    try unknownFields.traverse(visitor: &visitor)
  }

  static func ==(lhs: IAnts_RouteEntry, rhs: IAnts_RouteEntry) -> Bool {
    if lhs.destination != rhs.destination {return false}
    if lhs.gateway != rhs.gateway {return false}
    if lhs.netmask != rhs.netmask {return false}
    if lhs.interface != rhs.interface {return false}
    if lhs.flags != rhs.flags {return false}
    if lhs.metric != rhs.metric {return false}
    if lhs.isIpv6 != rhs.isIpv6 {return false}
    if lhs.hasMac_p != rhs.hasMac_p {return false}
    if lhs.macAddress != rhs.macAddress {return false}
    if lhs.unknownFields != rhs.unknownFields {return false}
    return true
  }
}

extension IAnts_UT_RoutingTableResponseMessage: SwiftProtobuf.Message, SwiftProtobuf._MessageImplementationBase, SwiftProtobuf._ProtoNameProviding {
  static let protoMessageName: String = _protobuf_package + ".UT_RoutingTableResponseMessage"
  static let _protobuf_nameMap: SwiftProtobuf._NameMap = [
    1: .same(proto: "routes"),
  ]

  mutating func decodeMessage<D: SwiftProtobuf.Decoder>(decoder: inout D) throws {
    while let fieldNumber = try decoder.nextFieldNumber() {
      // The use of inline closures is to circumvent an issue where the compiler
      // allocates stack space for every case branch when no optimizations are
      // enabled. https://github.com/apple/swift-protobuf/issues/1034
      switch fieldNumber {
      case 1: try { try decoder.decodeRepeatedMessageField(value: &self.routes) }()
      default: break
      }
    }
  }

  func traverse<V: SwiftProtobuf.Visitor>(visitor: inout V) throws {
    if !self.routes.isEmpty {
      try visitor.visitRepeatedMessageField(value: self.routes, fieldNumber: 1)
    }
    try unknownFields.traverse(visitor: &visitor)
  }

  static func ==(lhs: IAnts_UT_RoutingTableResponseMessage, rhs: IAnts_UT_RoutingTableResponseMessage) -> Bool {
    if lhs.routes != rhs.routes {return false}
    if lhs.unknownFields != rhs.unknownFields {return false}
    return true
  }
}

extension IAnts_UTMessage: SwiftProtobuf.Message, SwiftProtobuf._MessageImplementationBase, SwiftProtobuf._ProtoNameProviding {
  static let protoMessageName: String = _protobuf_package + ".UTMessage"
  static let _protobuf_nameMap: SwiftProtobuf._NameMap = [
    1: .standard(proto: "common_type"),
    2: .standard(proto: "ut_type"),
    10: .same(proto: "heartbeat"),
    11: .same(proto: "error"),
    20: .standard(proto: "ut_status"),
    21: .standard(proto: "ut_get_config"),
    22: .standard(proto: "ut_router_config"),
    23: .standard(proto: "ut_router_action"),
    24: .standard(proto: "ut_ip_config"),
    25: .standard(proto: "ut_proxy_config"),
    26: .standard(proto: "ut_proxy_action"),
    27: .standard(proto: "ut_proxy_status"),
    28: .standard(proto: "ut_default"),
    30: .standard(proto: "ut_start_pure_proxy"),
    31: .standard(proto: "ut_start_pure_capture"),
    32: .standard(proto: "ut_start_proxy_capture"),
    33: .standard(proto: "ut_stop_mode"),
    34: .standard(proto: "ut_mode_status"),
    40: .standard(proto: "ut_set_route_whitelist"),
    41: .standard(proto: "ut_set_route_blacklist"),
    42: .standard(proto: "ut_set_proxy_whitelist"),
    43: .standard(proto: "ut_set_proxy_blacklist"),
    44: .standard(proto: "ut_get_all_config"),
    45: .standard(proto: "ut_all_config_response"),
    46: .standard(proto: "ut_get_routing_table"),
    47: .standard(proto: "ut_routing_table_response"),
  ]

  mutating func decodeMessage<D: SwiftProtobuf.Decoder>(decoder: inout D) throws {
    while let fieldNumber = try decoder.nextFieldNumber() {
      // The use of inline closures is to circumvent an issue where the compiler
      // allocates stack space for every case branch when no optimizations are
      // enabled. https://github.com/apple/swift-protobuf/issues/1034
      switch fieldNumber {
      case 1: try {
        var v: IAnts_CommonMessageType?
        try decoder.decodeSingularEnumField(value: &v)
        if let v = v {
          if self.messageType != nil {try decoder.handleConflictingOneOf()}
          self.messageType = .commonType(v)
        }
      }()
      case 2: try {
        var v: IAnts_UTMessageType?
        try decoder.decodeSingularEnumField(value: &v)
        if let v = v {
          if self.messageType != nil {try decoder.handleConflictingOneOf()}
          self.messageType = .utType(v)
        }
      }()
      case 10: try {
        var v: IAnts_Heartbeat?
        var hadOneofValue = false
        if let current = self.body {
          hadOneofValue = true
          if case .heartbeat(let m) = current {v = m}
        }
        try decoder.decodeSingularMessageField(value: &v)
        if let v = v {
          if hadOneofValue {try decoder.handleConflictingOneOf()}
          self.body = .heartbeat(v)
        }
      }()
      case 11: try {
        var v: IAnts_ErrorMessage?
        var hadOneofValue = false
        if let current = self.body {
          hadOneofValue = true
          if case .error(let m) = current {v = m}
        }
        try decoder.decodeSingularMessageField(value: &v)
        if let v = v {
          if hadOneofValue {try decoder.handleConflictingOneOf()}
          self.body = .error(v)
        }
      }()
      case 20: try {
        var v: IAnts_UT_StatusMessage?
        var hadOneofValue = false
        if let current = self.body {
          hadOneofValue = true
          if case .utStatus(let m) = current {v = m}
        }
        try decoder.decodeSingularMessageField(value: &v)
        if let v = v {
          if hadOneofValue {try decoder.handleConflictingOneOf()}
          self.body = .utStatus(v)
        }
      }()
      case 21: try {
        var v: IAnts_UT_GetConfigMessage?
        var hadOneofValue = false
        if let current = self.body {
          hadOneofValue = true
          if case .utGetConfig(let m) = current {v = m}
        }
        try decoder.decodeSingularMessageField(value: &v)
        if let v = v {
          if hadOneofValue {try decoder.handleConflictingOneOf()}
          self.body = .utGetConfig(v)
        }
      }()
      case 22: try {
        var v: IAnts_UT_RouterConfigMessage?
        var hadOneofValue = false
        if let current = self.body {
          hadOneofValue = true
          if case .utRouterConfig(let m) = current {v = m}
        }
        try decoder.decodeSingularMessageField(value: &v)
        if let v = v {
          if hadOneofValue {try decoder.handleConflictingOneOf()}
          self.body = .utRouterConfig(v)
        }
      }()
      case 23: try {
        var v: IAnts_UT_RouterActionMessage?
        var hadOneofValue = false
        if let current = self.body {
          hadOneofValue = true
          if case .utRouterAction(let m) = current {v = m}
        }
        try decoder.decodeSingularMessageField(value: &v)
        if let v = v {
          if hadOneofValue {try decoder.handleConflictingOneOf()}
          self.body = .utRouterAction(v)
        }
      }()
      case 24: try {
        var v: IAnts_UT_IPConfigMessage?
        var hadOneofValue = false
        if let current = self.body {
          hadOneofValue = true
          if case .utIpConfig(let m) = current {v = m}
        }
        try decoder.decodeSingularMessageField(value: &v)
        if let v = v {
          if hadOneofValue {try decoder.handleConflictingOneOf()}
          self.body = .utIpConfig(v)
        }
      }()
      case 25: try {
        var v: IAnts_UT_ProxyConfigMessage?
        var hadOneofValue = false
        if let current = self.body {
          hadOneofValue = true
          if case .utProxyConfig(let m) = current {v = m}
        }
        try decoder.decodeSingularMessageField(value: &v)
        if let v = v {
          if hadOneofValue {try decoder.handleConflictingOneOf()}
          self.body = .utProxyConfig(v)
        }
      }()
      case 26: try {
        var v: IAnts_UT_ProxyActionMessage?
        var hadOneofValue = false
        if let current = self.body {
          hadOneofValue = true
          if case .utProxyAction(let m) = current {v = m}
        }
        try decoder.decodeSingularMessageField(value: &v)
        if let v = v {
          if hadOneofValue {try decoder.handleConflictingOneOf()}
          self.body = .utProxyAction(v)
        }
      }()
      case 27: try {
        var v: IAnts_UT_ProxyStatusMessage?
        var hadOneofValue = false
        if let current = self.body {
          hadOneofValue = true
          if case .utProxyStatus(let m) = current {v = m}
        }
        try decoder.decodeSingularMessageField(value: &v)
        if let v = v {
          if hadOneofValue {try decoder.handleConflictingOneOf()}
          self.body = .utProxyStatus(v)
        }
      }()
      case 28: try {
        var v: IAnts_UT_DefaultMessage?
        var hadOneofValue = false
        if let current = self.body {
          hadOneofValue = true
          if case .utDefault(let m) = current {v = m}
        }
        try decoder.decodeSingularMessageField(value: &v)
        if let v = v {
          if hadOneofValue {try decoder.handleConflictingOneOf()}
          self.body = .utDefault(v)
        }
      }()
      case 30: try {
        var v: IAnts_UT_StartPureProxyMessage?
        var hadOneofValue = false
        if let current = self.body {
          hadOneofValue = true
          if case .utStartPureProxy(let m) = current {v = m}
        }
        try decoder.decodeSingularMessageField(value: &v)
        if let v = v {
          if hadOneofValue {try decoder.handleConflictingOneOf()}
          self.body = .utStartPureProxy(v)
        }
      }()
      case 31: try {
        var v: IAnts_UT_StartPureCaptureMessage?
        var hadOneofValue = false
        if let current = self.body {
          hadOneofValue = true
          if case .utStartPureCapture(let m) = current {v = m}
        }
        try decoder.decodeSingularMessageField(value: &v)
        if let v = v {
          if hadOneofValue {try decoder.handleConflictingOneOf()}
          self.body = .utStartPureCapture(v)
        }
      }()
      case 32: try {
        var v: IAnts_UT_StartProxyCaptureMessage?
        var hadOneofValue = false
        if let current = self.body {
          hadOneofValue = true
          if case .utStartProxyCapture(let m) = current {v = m}
        }
        try decoder.decodeSingularMessageField(value: &v)
        if let v = v {
          if hadOneofValue {try decoder.handleConflictingOneOf()}
          self.body = .utStartProxyCapture(v)
        }
      }()
      case 33: try {
        var v: IAnts_UT_StopModeMessage?
        var hadOneofValue = false
        if let current = self.body {
          hadOneofValue = true
          if case .utStopMode(let m) = current {v = m}
        }
        try decoder.decodeSingularMessageField(value: &v)
        if let v = v {
          if hadOneofValue {try decoder.handleConflictingOneOf()}
          self.body = .utStopMode(v)
        }
      }()
      case 34: try {
        var v: IAnts_UT_ModeStatusMessage?
        var hadOneofValue = false
        if let current = self.body {
          hadOneofValue = true
          if case .utModeStatus(let m) = current {v = m}
        }
        try decoder.decodeSingularMessageField(value: &v)
        if let v = v {
          if hadOneofValue {try decoder.handleConflictingOneOf()}
          self.body = .utModeStatus(v)
        }
      }()
      case 40: try {
        var v: IAnts_UT_SetRouteWhitelistMessage?
        var hadOneofValue = false
        if let current = self.body {
          hadOneofValue = true
          if case .utSetRouteWhitelist(let m) = current {v = m}
        }
        try decoder.decodeSingularMessageField(value: &v)
        if let v = v {
          if hadOneofValue {try decoder.handleConflictingOneOf()}
          self.body = .utSetRouteWhitelist(v)
        }
      }()
      case 41: try {
        var v: IAnts_UT_SetRouteBlacklistMessage?
        var hadOneofValue = false
        if let current = self.body {
          hadOneofValue = true
          if case .utSetRouteBlacklist(let m) = current {v = m}
        }
        try decoder.decodeSingularMessageField(value: &v)
        if let v = v {
          if hadOneofValue {try decoder.handleConflictingOneOf()}
          self.body = .utSetRouteBlacklist(v)
        }
      }()
      case 42: try {
        var v: IAnts_UT_SetProxyWhitelistMessage?
        var hadOneofValue = false
        if let current = self.body {
          hadOneofValue = true
          if case .utSetProxyWhitelist(let m) = current {v = m}
        }
        try decoder.decodeSingularMessageField(value: &v)
        if let v = v {
          if hadOneofValue {try decoder.handleConflictingOneOf()}
          self.body = .utSetProxyWhitelist(v)
        }
      }()
      case 43: try {
        var v: IAnts_UT_SetProxyBlacklistMessage?
        var hadOneofValue = false
        if let current = self.body {
          hadOneofValue = true
          if case .utSetProxyBlacklist(let m) = current {v = m}
        }
        try decoder.decodeSingularMessageField(value: &v)
        if let v = v {
          if hadOneofValue {try decoder.handleConflictingOneOf()}
          self.body = .utSetProxyBlacklist(v)
        }
      }()
      case 44: try {
        var v: IAnts_UT_GetAllConfigMessage?
        var hadOneofValue = false
        if let current = self.body {
          hadOneofValue = true
          if case .utGetAllConfig(let m) = current {v = m}
        }
        try decoder.decodeSingularMessageField(value: &v)
        if let v = v {
          if hadOneofValue {try decoder.handleConflictingOneOf()}
          self.body = .utGetAllConfig(v)
        }
      }()
      case 45: try {
        var v: IAnts_UT_AllConfigResponseMessage?
        var hadOneofValue = false
        if let current = self.body {
          hadOneofValue = true
          if case .utAllConfigResponse(let m) = current {v = m}
        }
        try decoder.decodeSingularMessageField(value: &v)
        if let v = v {
          if hadOneofValue {try decoder.handleConflictingOneOf()}
          self.body = .utAllConfigResponse(v)
        }
      }()
      case 46: try {
        var v: IAnts_UT_GetRoutingTableMessage?
        var hadOneofValue = false
        if let current = self.body {
          hadOneofValue = true
          if case .utGetRoutingTable(let m) = current {v = m}
        }
        try decoder.decodeSingularMessageField(value: &v)
        if let v = v {
          if hadOneofValue {try decoder.handleConflictingOneOf()}
          self.body = .utGetRoutingTable(v)
        }
      }()
      case 47: try {
        var v: IAnts_UT_RoutingTableResponseMessage?
        var hadOneofValue = false
        if let current = self.body {
          hadOneofValue = true
          if case .utRoutingTableResponse(let m) = current {v = m}
        }
        try decoder.decodeSingularMessageField(value: &v)
        if let v = v {
          if hadOneofValue {try decoder.handleConflictingOneOf()}
          self.body = .utRoutingTableResponse(v)
        }
      }()
      default: break
      }
    }
  }

  func traverse<V: SwiftProtobuf.Visitor>(visitor: inout V) throws {
    // The use of inline closures is to circumvent an issue where the compiler
    // allocates stack space for every if/case branch local when no optimizations
    // are enabled. https://github.com/apple/swift-protobuf/issues/1034 and
    // https://github.com/apple/swift-protobuf/issues/1182
    switch self.messageType {
    case .commonType?: try {
      guard case .commonType(let v)? = self.messageType else { preconditionFailure() }
      try visitor.visitSingularEnumField(value: v, fieldNumber: 1)
    }()
    case .utType?: try {
      guard case .utType(let v)? = self.messageType else { preconditionFailure() }
      try visitor.visitSingularEnumField(value: v, fieldNumber: 2)
    }()
    case nil: break
    }
    switch self.body {
    case .heartbeat?: try {
      guard case .heartbeat(let v)? = self.body else { preconditionFailure() }
      try visitor.visitSingularMessageField(value: v, fieldNumber: 10)
    }()
    case .error?: try {
      guard case .error(let v)? = self.body else { preconditionFailure() }
      try visitor.visitSingularMessageField(value: v, fieldNumber: 11)
    }()
    case .utStatus?: try {
      guard case .utStatus(let v)? = self.body else { preconditionFailure() }
      try visitor.visitSingularMessageField(value: v, fieldNumber: 20)
    }()
    case .utGetConfig?: try {
      guard case .utGetConfig(let v)? = self.body else { preconditionFailure() }
      try visitor.visitSingularMessageField(value: v, fieldNumber: 21)
    }()
    case .utRouterConfig?: try {
      guard case .utRouterConfig(let v)? = self.body else { preconditionFailure() }
      try visitor.visitSingularMessageField(value: v, fieldNumber: 22)
    }()
    case .utRouterAction?: try {
      guard case .utRouterAction(let v)? = self.body else { preconditionFailure() }
      try visitor.visitSingularMessageField(value: v, fieldNumber: 23)
    }()
    case .utIpConfig?: try {
      guard case .utIpConfig(let v)? = self.body else { preconditionFailure() }
      try visitor.visitSingularMessageField(value: v, fieldNumber: 24)
    }()
    case .utProxyConfig?: try {
      guard case .utProxyConfig(let v)? = self.body else { preconditionFailure() }
      try visitor.visitSingularMessageField(value: v, fieldNumber: 25)
    }()
    case .utProxyAction?: try {
      guard case .utProxyAction(let v)? = self.body else { preconditionFailure() }
      try visitor.visitSingularMessageField(value: v, fieldNumber: 26)
    }()
    case .utProxyStatus?: try {
      guard case .utProxyStatus(let v)? = self.body else { preconditionFailure() }
      try visitor.visitSingularMessageField(value: v, fieldNumber: 27)
    }()
    case .utDefault?: try {
      guard case .utDefault(let v)? = self.body else { preconditionFailure() }
      try visitor.visitSingularMessageField(value: v, fieldNumber: 28)
    }()
    case .utStartPureProxy?: try {
      guard case .utStartPureProxy(let v)? = self.body else { preconditionFailure() }
      try visitor.visitSingularMessageField(value: v, fieldNumber: 30)
    }()
    case .utStartPureCapture?: try {
      guard case .utStartPureCapture(let v)? = self.body else { preconditionFailure() }
      try visitor.visitSingularMessageField(value: v, fieldNumber: 31)
    }()
    case .utStartProxyCapture?: try {
      guard case .utStartProxyCapture(let v)? = self.body else { preconditionFailure() }
      try visitor.visitSingularMessageField(value: v, fieldNumber: 32)
    }()
    case .utStopMode?: try {
      guard case .utStopMode(let v)? = self.body else { preconditionFailure() }
      try visitor.visitSingularMessageField(value: v, fieldNumber: 33)
    }()
    case .utModeStatus?: try {
      guard case .utModeStatus(let v)? = self.body else { preconditionFailure() }
      try visitor.visitSingularMessageField(value: v, fieldNumber: 34)
    }()
    case .utSetRouteWhitelist?: try {
      guard case .utSetRouteWhitelist(let v)? = self.body else { preconditionFailure() }
      try visitor.visitSingularMessageField(value: v, fieldNumber: 40)
    }()
    case .utSetRouteBlacklist?: try {
      guard case .utSetRouteBlacklist(let v)? = self.body else { preconditionFailure() }
      try visitor.visitSingularMessageField(value: v, fieldNumber: 41)
    }()
    case .utSetProxyWhitelist?: try {
      guard case .utSetProxyWhitelist(let v)? = self.body else { preconditionFailure() }
      try visitor.visitSingularMessageField(value: v, fieldNumber: 42)
    }()
    case .utSetProxyBlacklist?: try {
      guard case .utSetProxyBlacklist(let v)? = self.body else { preconditionFailure() }
      try visitor.visitSingularMessageField(value: v, fieldNumber: 43)
    }()
    case .utGetAllConfig?: try {
      guard case .utGetAllConfig(let v)? = self.body else { preconditionFailure() }
      try visitor.visitSingularMessageField(value: v, fieldNumber: 44)
    }()
    case .utAllConfigResponse?: try {
      guard case .utAllConfigResponse(let v)? = self.body else { preconditionFailure() }
      try visitor.visitSingularMessageField(value: v, fieldNumber: 45)
    }()
    case .utGetRoutingTable?: try {
      guard case .utGetRoutingTable(let v)? = self.body else { preconditionFailure() }
      try visitor.visitSingularMessageField(value: v, fieldNumber: 46)
    }()
    case .utRoutingTableResponse?: try {
      guard case .utRoutingTableResponse(let v)? = self.body else { preconditionFailure() }
      try visitor.visitSingularMessageField(value: v, fieldNumber: 47)
    }()
    case nil: break
    }
    try unknownFields.traverse(visitor: &visitor)
  }

  static func ==(lhs: IAnts_UTMessage, rhs: IAnts_UTMessage) -> Bool {
    if lhs.messageType != rhs.messageType {return false}
    if lhs.body != rhs.body {return false}
    if lhs.unknownFields != rhs.unknownFields {return false}
    return true
  }
}
