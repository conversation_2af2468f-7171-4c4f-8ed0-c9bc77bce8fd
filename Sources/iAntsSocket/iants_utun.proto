syntax = "proto3";

package iAnts;

import "iants.proto";

// UT 专用消息类型
enum UTMessageType {
  UT_STATUS = 0;
  UT_GET_CONFIG = 1;
  UT_ROUTER_CONFIG = 2;
  UT_ROUTER_ACTION = 3;
  UT_IP_CONFIG = 4;
  UT_PROXY_CONFIG = 5;
  UT_PROXY_ACTION = 6;
  UT_PROXY_STATUS = 7;

  // 新增三种模式相关消息类型
  UT_MODE_START_PURE_PROXY = 10;      // 启动纯代理模式
  UT_MODE_START_PURE_CAPTURE = 11;    // 启动纯抓包模式
  UT_MODE_START_PROXY_CAPTURE = 12;   // 启动代理抓包模式
  UT_MODE_STOP = 13;                  // 停止当前模式
  UT_MODE_STATUS = 14;                // 获取模式状态

  // 配置管理消息类型
  UT_CONFIG_SET_ROUTE_WHITELIST = 20; // 设置路由白名单
  UT_CONFIG_SET_ROUTE_BLACKLIST = 21; // 设置路由黑名单
  UT_CONFIG_SET_PROXY_WHITELIST = 22; // 设置代理白名单
  UT_CONFIG_SET_PROXY_BLACKLIST = 23; // 设置代理黑名单
  UT_CONFIG_GET_ALL = 24;             // 获取所有配置
  UT_CONFIG_GET_ROUTING_TABLE = 25;   // 获取路由表信息

  UT_DEFAULT = 99;
}

// UT 专用消息体
message UT_StatusMessage {
  bool utun_created = 1;
  string interface_name = 2;
  int32 unit = 3;
  string ip_address = 4;
  string dest_address = 5;
  int32 mtu = 6;
  bool interface_up = 7;
  bool proxy_running = 8;
  string proxy_url = 9;
  repeated string routes = 10;
}

message UT_GetConfigMessage {
  // 空消息
}

message UT_RouterConfigMessage {
  string strategy = 1;
  repeated string whitelist = 2;
  repeated string blacklist = 3;
  bool enable_dns_hijack = 4;
  string dns_server = 5;
}

message UT_RouterActionMessage {
  enum Action {
    ADD = 0;
    DELETE = 1;
    MODIFY = 2;
    RESTORE_DEFAULT = 3;
  }
  Action action = 1;
  string destination = 2;
  string gateway = 3;
  string netmask = 4;
  string interface = 5;
  int32 metric = 6;
}

message UT_IPConfigMessage {
  string ip_address = 1;
  string dest_address = 2;
  string netmask = 3;
  int32 mtu = 4;
  bool bring_up = 5;
}

message UT_ProxyConfigMessage {
  enum ProxyType {
    HTTP = 0;
    HTTPS = 1;
    SOCKS5 = 2;
    SHADOWSOCKS = 3;
  }
  ProxyType type = 1;
  string host = 2;
  int32 port = 3;
  string username = 4;
  string password = 5;
  string extra_config = 6;
}

message UT_ProxyActionMessage {
  enum Action {
    START = 0;
    STOP = 1;
    RESTART = 2;
  }
  Action action = 1;
}

message UT_ProxyStatusMessage {
  bool running = 1;
  string proxy_url = 2;
  int64 bytes_sent = 3;
  int64 bytes_received = 4;
  int64 start_time = 5;
}

message UT_DefaultMessage {
  // 空消息
}

// 新增：三种模式相关消息

// 代理模式枚举
enum ProxyMode {
  PURE_PROXY = 0;      // 纯代理模式
  PURE_CAPTURE = 1;    // 纯抓包模式
  PROXY_CAPTURE = 2;   // 代理抓包模式
}

// 代理状态枚举
enum ProxyModeState {
  MODE_STOPPED = 0;
  MODE_STARTING = 1;
  MODE_RUNNING = 2;
  MODE_STOPPING = 3;
  MODE_ERROR = 4;
}

// 过滤配置
message FilterConfig {
  repeated string allowed_domains = 1;  // 域名白名单
  repeated string blocked_domains = 2;  // 域名黑名单
  repeated string allowed_ips = 3;      // IP白名单 (支持CIDR)
  repeated string blocked_ips = 4;      // IP黑名单 (支持CIDR)
}

// 纯代理模式启动消息
message UT_StartPureProxyMessage {
  string proxy_url = 1;           // 代理地址 (必填)
  bool enable_udp = 2;            // 是否启动UDP转发 (默认true)
  bool global_proxy = 3;          // 是否全局代理 (默认false)
}

// 纯抓包模式启动消息
message UT_StartPureCaptureMessage {
  FilterConfig filter_config = 1;      // 过滤配置
  string remote_ws_server = 2;         // 远程WebSocket服务器 (可选)
  bool enable_udp = 3;                 // 是否启动UDP转发 (默认true)
  bool global_proxy = 4;               // 是否全局代理 (默认false)
}

// 代理抓包模式启动消息
message UT_StartProxyCaptureMessage {
  FilterConfig filter_config = 1;      // 过滤配置
  string proxy_url = 2;                // 代理地址 (必填)
  bool enable_udp = 3;                 // 是否启动UDP转发 (默认true)
  bool global_proxy = 4;               // 是否全局代理 (默认false)
  string remote_ws_server = 5;         // 远程WebSocket服务器 (可选)
}

// 停止模式消息
message UT_StopModeMessage {
  // 空消息
}

// 模式状态消息
message UT_ModeStatusMessage {
  ProxyModeState state = 1;            // 当前状态
  ProxyMode mode = 2;                  // 当前模式 (仅在运行时有效)
  string error_message = 3;            // 错误信息 (仅在错误状态时有效)
  int64 start_time = 4;                // 启动时间戳
  string proxy_url = 5;                // 代理URL (如果适用)
  string remote_ws_server = 6;         // 远程WebSocket服务器 (如果适用)
  bool enable_udp = 7;                 // UDP转发状态
  bool global_proxy = 8;               // 全局代理状态
}

// 配置管理相关消息

// 设置路由白名单消息
message UT_SetRouteWhitelistMessage {
  repeated string whitelist = 1;       // 路由白名单
}

// 设置路由黑名单消息
message UT_SetRouteBlacklistMessage {
  repeated string blacklist = 1;       // 路由黑名单
}

// 设置代理白名单消息
message UT_SetProxyWhitelistMessage {
  repeated string whitelist = 1;       // 代理白名单
}

// 设置代理黑名单消息
message UT_SetProxyBlacklistMessage {
  repeated string blacklist = 1;       // 代理黑名单
}

// 获取所有配置消息
message UT_GetAllConfigMessage {
  // 空消息
}

// 所有配置响应消息
message UT_AllConfigResponseMessage {
  repeated string route_whitelist = 1;  // 路由白名单
  repeated string route_blacklist = 2;  // 路由黑名单
  repeated string proxy_whitelist = 3;  // 代理白名单
  repeated string proxy_blacklist = 4;  // 代理黑名单
}

// 获取路由表消息
message UT_GetRoutingTableMessage {
  // 空消息
}

// 路由表条目
message RouteEntry {
  string destination = 1;              // 目标地址
  string gateway = 2;                  // 网关
  string netmask = 3;                  // 网络掩码
  string interface = 4;                // 接口名称
  int32 flags = 5;                     // 路由标志
  int32 metric = 6;                    // 路由度量
  bool is_ipv6 = 7;                    // 是否IPv6
  bool has_mac = 8;                    // 是否有MAC地址
  string mac_address = 9;              // MAC地址
}

// 路由表响应消息
message UT_RoutingTableResponseMessage {
  repeated RouteEntry routes = 1;      // 路由表条目
}

// UT 完整消息
message UTMessage {
  oneof message_type {
    CommonMessageType common_type = 1;
    UTMessageType ut_type = 2;
  }

  oneof body {
    // 公共消息
    Heartbeat heartbeat = 10;
    ErrorMessage error = 11;

    // UT 专用消息 - 原有消息
    UT_StatusMessage ut_status = 20;
    UT_GetConfigMessage ut_get_config = 21;
    UT_RouterConfigMessage ut_router_config = 22;
    UT_RouterActionMessage ut_router_action = 23;
    UT_IPConfigMessage ut_ip_config = 24;
    UT_ProxyConfigMessage ut_proxy_config = 25;
    UT_ProxyActionMessage ut_proxy_action = 26;
    UT_ProxyStatusMessage ut_proxy_status = 27;
    UT_DefaultMessage ut_default = 28;

    // 新增：三种模式相关消息
    UT_StartPureProxyMessage ut_start_pure_proxy = 30;
    UT_StartPureCaptureMessage ut_start_pure_capture = 31;
    UT_StartProxyCaptureMessage ut_start_proxy_capture = 32;
    UT_StopModeMessage ut_stop_mode = 33;
    UT_ModeStatusMessage ut_mode_status = 34;

    // 新增：配置管理消息
    UT_SetRouteWhitelistMessage ut_set_route_whitelist = 40;
    UT_SetRouteBlacklistMessage ut_set_route_blacklist = 41;
    UT_SetProxyWhitelistMessage ut_set_proxy_whitelist = 42;
    UT_SetProxyBlacklistMessage ut_set_proxy_blacklist = 43;
    UT_GetAllConfigMessage ut_get_all_config = 44;
    UT_AllConfigResponseMessage ut_all_config_response = 45;
    UT_GetRoutingTableMessage ut_get_routing_table = 46;
    UT_RoutingTableResponseMessage ut_routing_table_response = 47;
  }
}
