//
//  UtunProxy.swift
//  iAntsUT
//
//  Created by sun on 2024/12/17.
//

import Foundation

/// UTUN 设备状态
enum UtunStatus: Equatable {
    case closed
    case open
    case error(String)

    static func == (lhs: UtunStatus, rhs: UtunStatus) -> <PERSON><PERSON> {
        switch (lhs, rhs) {
        case (.closed, .closed), (.open, .open):
            return true
        case (.error(let lhsMessage), .error(let rhsMessage)):
            return lhsMessage == rhsMessage
        default:
            return false
        }
    }
}

/// 代理模式枚举
enum ProxyMode: String, CaseIterable {
    case pureProxy = "pure_proxy"           // 纯代理模式
    case pureCapture = "pure_capture"       // 纯抓包模式
    case proxyCapture = "proxy_capture"     // 代理抓包模式
}

/// 代理状态枚举
enum ProxyState: Equatable {
    case stopped
    case starting
    case running(ProxyMode)
    case stopping
    case error(String)

    static func == (lhs: ProxyState, rhs: ProxyState) -> <PERSON><PERSON> {
        switch (lhs, rhs) {
        case (.stopped, .stopped), (.starting, .starting), (.stopping, .stopping):
            return true
        case (.running(let lhsMode), .running(let rhsMode)):
            return lhsMode == rhsMode
        case (.error(let lhsMessage), .error(let rhsMessage)):
            return lhsMessage == rhsMessage
        default:
            return false
        }
    }
}

/// 路由备份条目
struct RouteBackupEntry {
    let destination: String
    let gateway: String
    let netmask: String
    let interface: String
    let flags: Int32
    let metric: Int32
}

/// 过滤配置
struct FilterConfig {
    let allowedDomains: [String]    // 域名白名单
    let blockedDomains: [String]    // 域名黑名单
    let allowedIPs: [String]        // IP白名单 (支持CIDR)
    let blockedIPs: [String]        // IP黑名单 (支持CIDR)

    static let empty = FilterConfig(allowedDomains: [], blockedDomains: [], allowedIPs: [], blockedIPs: [])
}

/// 代理配置
struct ProxyConfig {
    let proxyURL: String            // 代理地址
    let enableUDP: Bool             // UDP转发
    let globalProxy: Bool           // 全局代理
}

/// 抓包配置
struct CaptureConfig {
    let filterConfig: FilterConfig  // 过滤配置
    let remoteWSServer: String?     // 远程WebSocket服务器
    let enableUDP: Bool             // UDP转发
    let globalProxy: Bool           // 全局代理
}

/// 持久化配置
struct PersistentConfig: Codable {
    var routeWhitelist: [String] = []
    var routeBlacklist: [String] = []
    var proxyWhitelist: [String] = []
    var proxyBlacklist: [String] = []
}

/// 配置文件管理器
class ConfigManager {
    private let configURL: URL
    private let logger = DaLog(subsystem: "com.iants.ut", category: "ConfigManager", isPrint: true)

    init() {
        // 配置文件存储在应用支持目录
        let appSupport = FileManager.default.urls(for: .applicationSupportDirectory,
                                                 in: .userDomainMask).first!
        let iAntsDir = appSupport.appendingPathComponent("iAntsUT")

        // 确保目录存在
        try? FileManager.default.createDirectory(at: iAntsDir,
                                               withIntermediateDirectories: true,
                                               attributes: nil)

        configURL = iAntsDir.appendingPathComponent("utun_config.json")
        logger.info("配置文件路径: \(configURL.path)")
    }

    /// 加载配置
    func loadConfig() -> PersistentConfig {
        guard FileManager.default.fileExists(atPath: configURL.path) else {
            logger.info("配置文件不存在，使用默认配置")
            return PersistentConfig()
        }

        do {
            let data = try Data(contentsOf: configURL)
            let config = try JSONDecoder().decode(PersistentConfig.self, from: data)
            logger.info("配置文件加载成功")
            return config
        } catch {
            logger.error("配置文件加载失败: \(error)，使用默认配置")
            return PersistentConfig()
        }
    }

    /// 保存配置
    func saveConfig(_ config: PersistentConfig) throws {
        do {
            let data = try JSONEncoder().encode(config)
            try data.write(to: configURL)
            logger.info("配置文件保存成功")
        } catch {
            logger.error("配置文件保存失败: \(error)")
            throw error
        }
    }
}

/// 状态管理 Actor - 线程安全的状态管理
actor UtunStateManager {
    private(set) var utunStatus: UtunStatus = .closed
    private(set) var proxyState: ProxyState = .stopped
    private(set) var currentMode: ProxyMode?
    private(set) var startTime: Date?

    private var persistentConfig = PersistentConfig()

    func updateUtunStatus(_ status: UtunStatus) {
        utunStatus = status
    }

    func updateProxyState(_ state: ProxyState) {
        proxyState = state

        switch state {
        case .running(let mode):
            currentMode = mode
            if startTime == nil {
                startTime = Date()
            }
        case .stopped:
            currentMode = nil
            startTime = nil
        default:
            break
        }
    }

    func updatePersistentConfig(_ config: PersistentConfig) {
        persistentConfig = config
    }

    func getPersistentConfig() -> PersistentConfig {
        return persistentConfig
    }
}

/// UTUN 代理管理器 - 单例模式，现代化异步实现
final class UtunProxy {

    // MARK: - 单例
    static let shared = UtunProxy()

    // MARK: - 私有属性
    private let logger = DaLog(subsystem: "com.iants.ut", category: "UtunProxy", isPrint: true)
    private let operationQueue = DispatchQueue(label: "com.iants.ut.utun.operation", qos: .userInitiated)

    // MARK: - 状态管理
    private let stateManager: UtunStateManager

    // MARK: - 配置管理
    private let configManager = ConfigManager()

    // MARK: - UTUN 设备相关
    private var utunDevice = utun_device_t()

    // MARK: - 配置信息
    private var remoteAddress: String = ""
    private var localAddress: String = ""

    // MARK: - 路由备份
    private var routeBackups: [RouteBackupEntry] = []

    // MARK: - 状态监控任务
    private var statusMonitorTask: Task<Void, Never>?

    // MARK: - 初始化
    private init() {
        // 同步初始化 stateManager
        stateManager = UtunStateManager()
        logger.info("UtunProxy 初始化")

        // 加载持久化配置
        Task {
            let config = self.configManager.loadConfig()
            await self.stateManager.updatePersistentConfig(config)
        }

        startStatusMonitoring()
    }

    deinit {
        statusMonitorTask?.cancel()
        // 在 deinit 中只能同步关闭
        _ = utun_close(&utunDevice)
    }
}

// MARK: - 状态属性和访问方法

extension UtunProxy {

    /// 获取 UTUN 状态
    var utunStatus: UtunStatus {
        get async {
            await stateManager.utunStatus
        }
    }

    /// 获取代理状态
    var proxyState: ProxyState {
        get async {
            await stateManager.proxyState
        }
    }

    /// 获取当前模式
    var currentMode: ProxyMode? {
        get async {
            await stateManager.currentMode
        }
    }

    /// 获取 UTUN 文件描述符
    var utunFd: Int32 {
        return utunDevice.fd
    }

    /// 获取远程地址
    private func getRemoteAddress() -> String {
        return remoteAddress
    }

    /// 获取本地地址
    private func getLocalAddress() -> String {
        return localAddress
    }

    /// 获取 UTUN 接口名称
    var utunName: String {
        return withUnsafePointer(to: &utunDevice.ifname.0) { ptr in
            String(cString: ptr)
        }
    }

    /// 更新 UTUN 状态（内部使用）
    private func updateUtunStatus(_ status: UtunStatus) async {
        await stateManager.updateUtunStatus(status)
        logger.debug("UTUN 状态更新: \(status)")
    }

    /// 更新代理状态（内部使用）
    private func updateProxyState(_ state: ProxyState) async {
        await stateManager.updateProxyState(state)
        logger.debug("代理状态更新: \(state)")
    }
}

// MARK: - 状态监控

extension UtunProxy {

    /// 启动状态监控
    private func startStatusMonitoring() {
        statusMonitorTask = Task { [weak self] in
            guard let self = self else { return }

            while !Task.isCancelled {
                await self.checkUtunStatus()

                do {
                    try await Task.sleep(nanoseconds: 30_000_000_000) // 30秒检查一次
                } catch {
                    if !Task.isCancelled {
                        self.logger.error("状态监控任务睡眠失败: \(error)")
                    }
                    break
                }
            }
        }
    }

    /// 检查 UTUN 状态 - 现代化异步实现
    private func checkUtunStatus() async {
        let isOpen = utun_is_open(&utunDevice)
        let currentStatus = await stateManager.utunStatus

        switch currentStatus {
        case .open:
            if !isOpen {
                await updateUtunStatus(.closed)
                logger.warning("检测到 UTUN 设备已关闭")
            }
        case .closed:
            if isOpen {
                await updateUtunStatus(.open)
                logger.info("检测到 UTUN 设备已打开")
            }
        case .error:
            if isOpen {
                await updateUtunStatus(.open)
                logger.info("UTUN 设备从错误状态恢复")
            }
        }
    }
}

// MARK: - UTUN 设备管理
extension UtunProxy {

    /// 创建 UTUN 设备（自动分配单元号）- 现代化异步实现
    func createUtun() async throws {
        logger.info("开始创建 UTUN 设备")

        // 检查当前状态
        let currentStatus = await stateManager.utunStatus
        if case .open = currentStatus {
            logger.warning("UTUN 设备已经打开")
            return
        }

        // 在后台队列执行 C 函数调用
        let result = await Task.detached { [utunDevice = self.utunDevice] in
            var device = utunDevice
            return utun_create_auto(&device)
        }.value

        if result == 0 {
            await updateUtunStatus(.open)
            let ifname = withUnsafePointer(to: &utunDevice.ifname.0) { ptr in
                String(cString: ptr)
            }
            logger.info("UTUN 设备创建成功: fd=\(utunDevice.fd), name=\(ifname)")
        } else {
            let errorMsg = "创建 UTUN 设备失败，错误码: \(result)"
            await updateUtunStatus(.error(errorMsg))
            logger.error(errorMsg)
            throw UtunError.createFailed(result)
        }
    }

    /// 关闭 UTUN 设备 - 现代化异步实现
    func closeUtun() async {
        logger.info("开始关闭 UTUN 设备")

        // 先停止代理
        let currentState = await stateManager.proxyState
        if case .running = currentState {
            _ = tun2proxy_stop()
            await updateProxyState(.stopped)
            logger.info("已停止代理服务")
        }

        // 在后台队列关闭 UTUN 设备
        let result = await Task.detached { [utunDevice = self.utunDevice] in
            var device = utunDevice
            return utun_close(&device)
        }.value

        if result == 0 {
            await updateUtunStatus(.closed)
            logger.info("UTUN 设备关闭成功")
        } else {
            let errorMsg = "关闭 UTUN 设备失败，错误码: \(result)"
            await updateUtunStatus(.error(errorMsg))
            logger.error(errorMsg)
        }
    }
}

// MARK: - UTUN 配置
extension UtunProxy {

    /// 配置 UTUN 点对点模式 - 现代化异步实现
    /// - Parameters:
    ///   - localAddr: 本地地址
    ///   - remoteAddr: 远程地址
    ///   - mtu: MTU 大小（可选）
    func configureUtun(localAddr: String, remoteAddr: String, mtu: Int? = nil) async throws {
        let currentStatus = await stateManager.utunStatus
        guard case .open = currentStatus else {
            throw UtunError.deviceNotOpen
        }

        let ifname = withUnsafePointer(to: &utunDevice.ifname.0) { ptr in
            String(cString: ptr)
        }
        logger.info("配置 UTUN 接口: \(ifname), 本地: \(localAddr), 远程: \(remoteAddr)")

        // 保存地址信息
        self.localAddress = localAddr
        self.remoteAddress = remoteAddr

        // 在后台队列执行配置操作
        try await Task.detached {
            // 设置点对点地址
            let result = interface_set_pointopoint(ifname, localAddr, remoteAddr)
            if result != 0 {
                let errorMsg = "设置点对点地址失败，错误码: \(result)"
                throw UtunError.configurationFailed(errorMsg)
            }

            // 设置 MTU（如果指定）
            if let mtu = mtu {
                let mtuResult = interface_set_mtu(ifname, Int32(mtu))
                if mtuResult != 0 {
                    print("设置 MTU 失败，错误码: \(mtuResult)")
                } else {
                    print("MTU 设置为: \(mtu)")
                }
            }

            // 启用接口
            let upResult = interface_up(ifname)
            if upResult != 0 {
                let errorMsg = "启用接口失败，错误码: \(upResult)"
                throw UtunError.configurationFailed(errorMsg)
            }

            // 刷新接口信息
            var config = interface_config_t()
            let getResult = interface_get(ifname, &config)
            if getResult == 0 {
                interface_config_free(&config)
            }
        }.value

        logger.info("UTUN 配置完成")
    }
}

// MARK: - 错误定义
enum UtunError: Error, LocalizedError {
    case deviceNotOpen
    case createFailed(Int32)
    case configurationFailed(String)
    case routingFailed(String)
    case proxyFailed(String)
    case internalError(String)

    var errorDescription: String? {
        switch self {
        case .deviceNotOpen:
            return "UTUN 设备未打开"
        case .createFailed(let code):
            return "创建 UTUN 设备失败，错误码: \(code)"
        case .configurationFailed(let message):
            return "配置失败: \(message)"
        case .routingFailed(let message):
            return "路由操作失败: \(message)"
        case .proxyFailed(let message):
            return "代理操作失败: \(message)"
        case .internalError(let message):
            return "内部错误: \(message)"
        }
    }
}

// MARK: - 路由管理
extension UtunProxy {

    /// 添加路由规则
    /// - Parameters:
    ///   - destination: 目标网络/主机
    ///   - gateway: 网关地址（可选）
    ///   - netmask: 网络掩码（可选）
    ///   - interface: 接口名称（可选，默认使用当前 UTUN）
    ///   - metric: 路由度量值（可选）
    func addRoute(destination: String, gateway: String? = nil, netmask: String? = nil, interface: String? = nil, metric: Int32 = 0) async throws {
        try await withCheckedThrowingContinuation { (continuation: CheckedContinuation<Void, Error>) in
            operationQueue.async { [weak self] in
                guard let self = self else {
                    continuation.resume(throwing: UtunError.internalError("实例已释放"))
                    return
                }

                let targetInterface = interface ?? withUnsafePointer(to: &self.utunDevice.ifname.0) { ptr in
                    String(cString: ptr)
                }
                self.logger.info("添加路由: \(destination) -> \(targetInterface)")

                let result: Int32

                if let gateway = gateway, let netmask = netmask {
                    // 网络路由
                    result = route_add_network_route(destination, netmask, gateway, targetInterface)
                } else if let gateway = gateway {
                    // 主机路由
                    result = route_add_host_route(destination, gateway, targetInterface)
                } else {
                    // 直接路由到接口
                    result = route_add_host_route(destination, "0.0.0.0", targetInterface)
                }

                if result != 0 {
                    let errorMsg = "添加路由失败，错误码: \(result)"
                    self.logger.error(errorMsg)
                    continuation.resume(throwing: UtunError.routingFailed(errorMsg))
                } else {
                    self.logger.info("路由添加成功")
                    continuation.resume()
                }
            }
        }
    }

    /// 删除路由规则
    /// - Parameters:
    ///   - destination: 目标网络/主机
    ///   - gateway: 网关地址（可选）
    ///   - netmask: 网络掩码（可选）
    func deleteRoute(destination: String, gateway: String? = nil, netmask: String? = nil) async throws {
        try await withCheckedThrowingContinuation { (continuation: CheckedContinuation<Void, Error>) in
            operationQueue.async { [weak self] in
                guard let self = self else {
                    continuation.resume(throwing: UtunError.internalError("实例已释放"))
                    return
                }

                self.logger.info("删除路由: \(destination)")

                let result = route_delete_route(destination, gateway ?? "", netmask ?? "")

                if result != 0 {
                    let errorMsg = "删除路由失败，错误码: \(result)"
                    self.logger.error(errorMsg)
                    continuation.resume(throwing: UtunError.routingFailed(errorMsg))
                } else {
                    self.logger.info("路由删除成功")
                    continuation.resume()
                }
            }
        }
    }

    /// 获取路由表
    func getRoutingTable() async throws -> [RouteInfo] {
        return try await withCheckedThrowingContinuation { (continuation: CheckedContinuation<[RouteInfo], Error>) in
            operationQueue.async { [weak self] in
                guard let self = self else {
                    continuation.resume(throwing: UtunError.internalError("实例已释放"))
                    return
                }

                var entries: UnsafeMutablePointer<route_entry_t>?
                var count: Int32 = 0

                let result = route_get_routing_table(&entries, &count)

                if result != 0 {
                    let errorMsg = "获取路由表失败，错误码: \(result)"
                    self.logger.error(errorMsg)
                    continuation.resume(throwing: UtunError.routingFailed(errorMsg))
                    return
                }

                var routeInfos: [RouteInfo] = []

                if let entries = entries {
                    for index in 0..<Int(count) {
                        let entry = entries[index]
                        let routeInfo = RouteInfo(from: entry)
                        routeInfos.append(routeInfo)
                    }
                    free(entries)
                }

                self.logger.info("获取到 \(routeInfos.count) 条路由信息")
                continuation.resume(returning: routeInfos)
            }
        }
    }

    /// 获取指定目标的路由信息
    /// - Parameter destination: 目标地址
    func getRouteInfo(for destination: String) async throws -> RouteInfo? {
        return try await withCheckedThrowingContinuation { (continuation: CheckedContinuation<RouteInfo?, Error>) in
            operationQueue.async { [weak self] in
                guard let self = self else {
                    continuation.resume(throwing: UtunError.internalError("实例已释放"))
                    return
                }

                var entry = route_entry_t()
                let result = route_get_route_info(destination, &entry)

                if result != 0 {
                    if result == -1 {
                        // 未找到路由
                        continuation.resume(returning: nil)
                    } else {
                        let errorMsg = "获取路由信息失败，错误码: \(result)"
                        self.logger.error(errorMsg)
                        continuation.resume(throwing: UtunError.routingFailed(errorMsg))
                    }
                    return
                }

                let routeInfo = RouteInfo(from: entry)
                continuation.resume(returning: routeInfo)
            }
        }
    }

    /// 清空路由表
    func flushRoutingTable() async throws {
        try await withCheckedThrowingContinuation { (continuation: CheckedContinuation<Void, Error>) in
            operationQueue.async { [weak self] in
                guard let self = self else {
                    continuation.resume(throwing: UtunError.internalError("实例已释放"))
                    return
                }

                self.logger.warning("清空路由表")

                let result = route_flush_table()

                if result != 0 {
                    let errorMsg = "清空路由表失败，错误码: \(result)"
                    self.logger.error(errorMsg)
                    continuation.resume(throwing: UtunError.routingFailed(errorMsg))
                } else {
                    self.logger.info("路由表清空成功")
                    continuation.resume()
                }
            }
        }
    }
}

// MARK: - 路由备份与恢复
extension UtunProxy {

    /// 备份当前路由表
    func backupRoutingTable() async throws {
        try await withCheckedThrowingContinuation { (continuation: CheckedContinuation<Void, Error>) in
            operationQueue.async { [weak self] in
                guard let self = self else {
                    continuation.resume(throwing: UtunError.internalError("实例已释放"))
                    return
                }

                self.logger.info("开始备份路由表")

                var entries: UnsafeMutablePointer<route_entry_t>?
                var count: Int32 = 0

                let result = route_get_routing_table(&entries, &count)

                if result != 0 {
                    let errorMsg = "获取路由表失败，错误码: \(result)"
                    self.logger.error(errorMsg)
                    continuation.resume(throwing: UtunError.routingFailed(errorMsg))
                    return
                }

                self.routeBackupLock.lock()
                self.routeBackups.removeAll()

                if let entries = entries {
                    for index in 0..<Int(count) {
                        var entry = entries[index]

                        // 转换地址
                        let destination = String(cString: inet_ntoa(entry.destination.sin_addr))
                        let gateway = String(cString: inet_ntoa(entry.gateway.sin_addr))
                        let netmask = String(cString: inet_ntoa(entry.netmask.sin_addr))
                        let interface = withUnsafePointer(to: &entry.interface.0) { ptr in
                            String(cString: ptr)
                        }

                        let backup = RouteBackupEntry(
                            destination: destination,
                            gateway: gateway,
                            netmask: netmask,
                            interface: interface,
                            flags: entry.flags,
                            metric: entry.metric
                        )

                        self.routeBackups.append(backup)
                    }
                    free(entries)
                }

                self.routeBackupLock.unlock()

                self.logger.info("路由表备份完成，共备份 \(self.routeBackups.count) 条路由")
                continuation.resume()
            }
        }
    }

    /// 恢复路由表
    func restoreRoutingTable() async throws {
        try await withCheckedThrowingContinuation { (continuation: CheckedContinuation<Void, Error>) in
            operationQueue.async { [weak self] in
                guard let self = self else {
                    continuation.resume(throwing: UtunError.internalError("实例已释放"))
                    return
                }

                self.routeBackupLock.lock()
                let backups = self.routeBackups
                self.routeBackupLock.unlock()

                if backups.isEmpty {
                    self.logger.warning("没有路由备份可恢复")
                    continuation.resume()
                    return
                }

                self.logger.info("开始恢复路由表，共 \(backups.count) 条路由")

                var successCount = 0
                var failureCount = 0

                for backup in backups {
                    let result = route_add_route_entry(
                        backup.destination,
                        backup.gateway,
                        backup.netmask,
                        backup.interface,
                        backup.flags,
                        backup.metric
                    )

                    if result == 0 {
                        successCount += 1
                    } else {
                        failureCount += 1
                        self.logger.warning("恢复路由失败: \(backup.destination) -> \(backup.interface)")
                    }
                }

                self.logger.info("路由表恢复完成，成功: \(successCount), 失败: \(failureCount)")

                if failureCount > 0 {
                    continuation.resume(throwing: UtunError.routingFailed("部分路由恢复失败"))
                } else {
                    continuation.resume()
                }
            }
        }
    }

    /// 获取路由备份数量
    var routeBackupCount: Int {
        routeBackupLock.lock()
        defer { routeBackupLock.unlock() }
        return routeBackups.count
    }
}

// MARK: - 便捷路由方法
extension UtunProxy {

    /// 转发特定网络流量到 UTUN
    /// - Parameters:
    ///   - networks: 要转发的网络列表（CIDR 格式，如 "10.0.0.0/8"）
    ///   - backup: 是否备份当前路由表
    func redirectNetworksToUtun(_ networks: [String], backup: Bool = true) async throws {
        guard case .open = utunStatus else {
            throw UtunError.deviceNotOpen
        }

        if backup {
            try await backupRoutingTable()
        }

        let ifname = withUnsafePointer(to: &utunDevice.ifname.0) { ptr in
            String(cString: ptr)
        }
        logger.info("转发网络流量到 UTUN: \(ifname)")

        for network in networks {
            let components = network.split(separator: "/")
            guard components.count == 2,
                  let prefixLength = Int(components[1]) else {
                logger.error("无效的网络格式: \(network)")
                continue
            }

            let networkAddr = String(components[0])
            let netmask = cidrToNetmask(prefixLength)

            do {
                try await addRoute(destination: networkAddr, netmask: netmask, interface: ifname)
                logger.info("已添加路由: \(network) -> \(ifname)")
            } catch {
                logger.error("添加路由失败: \(network), 错误: \(error)")
            }
        }
    }

    /// 转发所有流量到 UTUN（默认路由）
    /// - Parameter backup: 是否备份当前路由表
    func redirectAllTrafficToUtun(backup: Bool = true) async throws {
        guard case .open = utunStatus else {
            throw UtunError.deviceNotOpen
        }

        if backup {
            try await backupRoutingTable()
        }

        let ifname = withUnsafePointer(to: &utunDevice.ifname.0) { ptr in
            String(cString: ptr)
        }
        logger.info("转发所有流量到 UTUN: \(ifname)")

        // 对于UTUN接口，我们使用分段路由的方式来避免路由冲突
        // 添加 0.0.0.0/1 和 *********/1 来覆盖所有流量
        let remoteAddr = getRemoteAddress()

        if !remoteAddr.isEmpty {
            logger.info("使用远程地址作为网关: \(remoteAddr)")

            // 添加 0.0.0.0/1 路由
            var result = route_add_network_route("0.0.0.0", "*********", remoteAddr, ifname)
            if result != 0 {
                let errorMsg = "添加 0.0.0.0/1 路由失败，错误码: \(result)"
                logger.error(errorMsg)
                throw UtunError.routingFailed(errorMsg)
            }
            logger.info("已添加 0.0.0.0/1 路由")

            // 添加 *********/1 路由
            result = route_add_network_route("*********", "*********", remoteAddr, ifname)
            if result != 0 {
                let errorMsg = "添加 *********/1 路由失败，错误码: \(result)"
                logger.error(errorMsg)
                throw UtunError.routingFailed(errorMsg)
            }
            logger.info("已添加 *********/1 路由")
        } else {
            logger.warning("未找到远程地址，跳过默认路由设置")
            throw UtunError.configurationFailed("未配置远程地址")
        }

        logger.info("默认路由已设置到 UTUN")
    }

    /// CIDR 前缀长度转换为网络掩码
    private func cidrToNetmask(_ prefixLength: Int) -> String {
        guard prefixLength >= 0 && prefixLength <= 32 else {
            return "255.255.255.255"
        }

        let mask = (0xFFFFFFFF << (32 - prefixLength)) & 0xFFFFFFFF
        let octet1 = (mask >> 24) & 0xFF
        let octet2 = (mask >> 16) & 0xFF
        let octet3 = (mask >> 8) & 0xFF
        let octet4 = mask & 0xFF

        return "\(octet1).\(octet2).\(octet3).\(octet4)"
    }
}

// MARK: - 代理管理
extension UtunProxy {

    /// 启动代理
    /// - Parameters:
    ///   - proxyUrl: 代理 URL（如 "socks5://127.0.0.1:1080"）
    ///   - mtu: MTU 大小（默认 1500）
    ///   - dnsStrategy: DNS 策略（默认 OverTcp）
    ///   - verbosity: 日志级别（默认 Info）
    ///   - backupRoutes: 是否备份路由表（默认 true）
    func startProxy(proxyUrl: String, mtu: UInt16 = 1500, dnsStrategy: Tun2proxyDns = Tun2proxyDns_OverTcp, verbosity: Tun2proxyVerbosity = Tun2proxyVerbosity_Info, backupRoutes: Bool = true) async throws {

        guard case .open = utunStatus else {
            throw UtunError.deviceNotOpen
        }

        guard case .stopped = proxyStatus else {
            throw UtunError.proxyFailed("代理已在运行中")
        }

        if backupRoutes {
            try await backupRoutingTable()
        }

        try await startProxyInternal(proxyUrl: proxyUrl, mtu: mtu, dnsStrategy: dnsStrategy, verbosity: verbosity)
    }

    /// 内部代理启动实现
    private func startProxyInternal(proxyUrl: String, mtu: UInt16, dnsStrategy: Tun2proxyDns, verbosity: Tun2proxyVerbosity) async throws {
        try await withCheckedThrowingContinuation { (continuation: CheckedContinuation<Void, Error>) in
            operationQueue.async { [weak self] in
                guard let self = self else {
                    continuation.resume(throwing: UtunError.internalError("实例已释放"))
                    return
                }

                self.logger.info("启动代理: \(proxyUrl)")

                // 设置回调
                self.setupProxyCallbacks()

                // 启动代理
                let result = tun2proxy_with_fd_run(
                    proxyUrl,
                    self.utunDevice.fd,
                    false, // 不自动关闭 fd
                    true,  // iOS 需要包信息头
                    mtu,
                    dnsStrategy,
                    verbosity
                )

                if result == 0 {
                    Task {
                        await self.updateProxyState(.running(.pureProxy)) // 临时使用 pureProxy
                    }
                    self.logger.info("代理启动成功")
                    continuation.resume()
                } else {
                    let errorMsg = "代理启动失败，错误码: \(result)"
                    Task {
                        await self.updateProxyState(.error(errorMsg))
                    }
                    self.logger.error(errorMsg)
                    continuation.resume(throwing: UtunError.proxyFailed(errorMsg))
                }
            }
        }
    }

    /// 设置代理回调
    private func setupProxyCallbacks() {
        // 设置日志回调 - 使用简单的打印，不捕获 self
        tun2proxy_set_log_callback({ level, message, _ in
            guard let message = message else { return }
            let msg = String(cString: message)

            switch level {
            case Tun2proxyVerbosity_Error:
                print("[tun2proxy ERROR] \(msg)")
            case Tun2proxyVerbosity_Warn:
                print("[tun2proxy WARN] \(msg)")
            case Tun2proxyVerbosity_Info:
                print("[tun2proxy INFO] \(msg)")
            case Tun2proxyVerbosity_Debug:
                print("[tun2proxy DEBUG] \(msg)")
            case Tun2proxyVerbosity_Trace:
                print("[tun2proxy TRACE] \(msg)")
            default:
                print("[tun2proxy] \(msg)")
            }
        }, nil)

        // 设置流量统计回调 - 使用简单的打印，不捕获 self
        tun2proxy_set_traffic_status_callback(5, { status, _ in
            guard let status = status else { return }
            print("[流量统计] TX: \(status.pointee.tx) bytes, RX: \(status.pointee.rx) bytes")
        }, nil)
    }

    /// 停止代理
    func stopProxy() async {
        await withCheckedContinuation { continuation in
            operationQueue.async { [weak self] in
                guard let self = self else {
                    continuation.resume()
                    return
                }

                guard case .running = self.proxyStatus else {
                    self.logger.info("代理未运行")
                    continuation.resume()
                    return
                }

                self.logger.info("停止代理")

                let result = tun2proxy_stop()

                if result == 0 {
                    Task {
                        await self.updateProxyState(.stopped)
                    }
                    self.logger.info("代理停止成功")
                } else {
                    let errorMsg = "代理停止失败，错误码: \(result)"
                    Task {
                        await self.updateProxyState(.error(errorMsg))
                    }
                    self.logger.error(errorMsg)
                }

                continuation.resume()
            }
        }
    }

    /// 重启代理
    /// - Parameters:
    ///   - proxyUrl: 新的代理 URL
    ///   - mtu: MTU 大小
    ///   - dnsStrategy: DNS 策略
    ///   - verbosity: 日志级别
    func restartProxy(proxyUrl: String, mtu: UInt16 = 1500, dnsStrategy: Tun2proxyDns = Tun2proxyDns_OverTcp, verbosity: Tun2proxyVerbosity = Tun2proxyVerbosity_Info) async throws {

        await stopProxy()

        // 等待一下确保完全停止
        try await Task.sleep(nanoseconds: 1_000_000_000) // 1秒

        try await startProxy(proxyUrl: proxyUrl, mtu: mtu, dnsStrategy: dnsStrategy, verbosity: verbosity, backupRoutes: false)
    }
}

// MARK: - 辅助结构和方法
extension UtunProxy {



    /// 完整的代理设置流程
    /// - Parameters:
    ///   - proxyUrl: 代理 URL
    ///   - localAddr: 本地地址
    ///   - remoteAddr: 远程地址
    ///   - networks: 要转发的网络（可选，为空则转发所有流量）
    ///   - mtu: MTU 大小
    func setupCompleteProxy(proxyUrl: String, localAddr: String, remoteAddr: String, networks: [String]? = nil, mtu: Int = 1500) async throws {

        logger.info("开始完整代理设置流程")

        // 1. 创建 UTUN 设备
        try await createUtun()

        // 2. 配置 UTUN 接口
        try await configureUtun(localAddr: localAddr, remoteAddr: remoteAddr, mtu: mtu)

        // 3. 备份路由表
        try await backupRoutingTable()

        // 4. 设置路由转发
        if let networks = networks {
            try await redirectNetworksToUtun(networks, backup: false)
        } else {
            try await redirectAllTrafficToUtun(backup: false)
        }

        // 5. 启动代理
        try await startProxy(proxyUrl: proxyUrl, mtu: UInt16(mtu), backupRoutes: false)

        logger.info("完整代理设置完成")
    }

    /// 关闭代理并恢复网络状态
    func teardownProxy() async throws {
        logger.info("开始关闭代理并恢复网络状态")

        // 1. 停止代理
        await stopProxy()

        // 2. 恢复路由表
        if routeBackupCount > 0 {
            try await restoreRoutingTable()
        }

        // 3. 关闭 UTUN 设备
        await closeUtun()

        logger.info("代理关闭和网络状态恢复完成")
    }
}

// MARK: - 辅助数据结构
struct UtunInfo {
    let status: UtunStatus
    let fileDescriptor: Int32
    let name: String
    let proxyStatus: ProxyState
}

struct RouteInfo {
    let destination: String
    let gateway: String
    let netmask: String
    let interface: String
    let flags: Int32
    let metric: Int32
    let isIPv6: Bool
    let hasMac: Bool
    let macAddress: String

    init(from entry: route_entry_t) {
        // 创建可变副本来处理 C 结构体
        var mutableEntry = entry

        if entry.is_ipv6 != 0 {
            // IPv6 路由
            self.isIPv6 = true
            var buffer = [Int8](repeating: 0, count: Int(INET6_ADDRSTRLEN))
            inet_ntop(AF_INET6, &mutableEntry.destination6.sin6_addr, &buffer, socklen_t(INET6_ADDRSTRLEN))
            self.destination = String(cString: buffer)

            inet_ntop(AF_INET6, &mutableEntry.gateway6.sin6_addr, &buffer, socklen_t(INET6_ADDRSTRLEN))
            self.gateway = String(cString: buffer)

            inet_ntop(AF_INET6, &mutableEntry.netmask6.sin6_addr, &buffer, socklen_t(INET6_ADDRSTRLEN))
            self.netmask = String(cString: buffer)
        } else {
            // IPv4 路由
            self.isIPv6 = false
            self.destination = String(cString: inet_ntoa(mutableEntry.destination.sin_addr))
            self.gateway = String(cString: inet_ntoa(mutableEntry.gateway.sin_addr))
            self.netmask = String(cString: inet_ntoa(mutableEntry.netmask.sin_addr))
        }

        // 使用已有的 mutableEntry 来获取 C 字符串
        self.interface = withUnsafePointer(to: &mutableEntry.interface.0) { ptr in
            String(cString: ptr)
        }
        self.flags = entry.flags
        self.metric = entry.metric
        self.hasMac = entry.has_mac != 0
        self.macAddress = withUnsafePointer(to: &mutableEntry.mac_address.0) { ptr in
            String(cString: ptr)
        }
    }
}

// MARK: - 三种模式核心接口
extension UtunProxy {

    /// 启动纯代理模式
    /// - Parameter config: 代理配置
    func startPureProxy(config: ProxyConfig) async throws {
        logger.info("启动纯代理模式: \(config.proxyURL)")

        // 检查当前状态，确保模式互斥
        let currentState = await stateManager.proxyState
        guard case .stopped = currentState else {
            throw UtunError.proxyFailed("当前已有模式在运行，请先停止: \(currentState)")
        }

        await updateProxyState(.starting)

        do {
            // 1. 创建和配置 UTUN 设备
            try await createUtun()
            try await configureUtun(localAddr: "********", remoteAddr: "********")

            // 2. 设置路由（根据 globalProxy 决定）
            if config.globalProxy {
                try await redirectAllTrafficToUtun()
            }

            // 3. 启动代理服务
            try await startProxyInternal(
                proxyUrl: config.proxyURL,
                mtu: 1500,
                dnsStrategy: Tun2proxyDns_OverTcp,
                verbosity: Tun2proxyVerbosity_Info
            )

            await updateProxyState(.running(.pureProxy))
            logger.info("纯代理模式启动成功")

        } catch {
            await updateProxyState(.error(error.localizedDescription))
            logger.error("纯代理模式启动失败: \(error)")
            throw error
        }
    }

    /// 启动纯抓包模式
    /// - Parameter config: 抓包配置
    func startPureCapture(config: CaptureConfig) async throws {
        logger.info("启动纯抓包模式")

        // 检查当前状态，确保模式互斥
        let currentState = await stateManager.proxyState
        guard case .stopped = currentState else {
            throw UtunError.proxyFailed("当前已有模式在运行，请先停止: \(currentState)")
        }

        await updateProxyState(.starting)

        do {
            // 1. 创建和配置 UTUN 设备
            try await createUtun()
            try await configureUtun(localAddr: "********", remoteAddr: "********")

            // 2. 设置路由（根据 globalProxy 决定）
            if config.globalProxy {
                try await redirectAllTrafficToUtun()
            }

            // 3. 启动代理到本地 MITM（默认转发到本地抓包模块）
            let mitmProxyUrl = "http://127.0.0.1:8080" // 本地 MITM 代理地址
            try await startProxyInternal(
                proxyUrl: mitmProxyUrl,
                mtu: 1500,
                dnsStrategy: Tun2proxyDns_OverTcp,
                verbosity: Tun2proxyVerbosity_Info
            )

            // 4. 如果有远程 WebSocket 服务器，启动转发
            if let remoteWS = config.remoteWSServer, !remoteWS.isEmpty {
                // TODO: 启动 WebSocket 转发逻辑
                logger.info("配置远程 WebSocket 转发: \(remoteWS)")
            }

            await updateProxyState(.running(.pureCapture))
            logger.info("纯抓包模式启动成功")

        } catch {
            await updateProxyState(.error(error.localizedDescription))
            logger.error("纯抓包模式启动失败: \(error)")
            throw error
        }
    }

    /// 启动代理抓包模式
    /// - Parameters:
    ///   - proxyConfig: 代理配置
    ///   - captureConfig: 抓包配置
    func startProxyCapture(proxyConfig: ProxyConfig, captureConfig: CaptureConfig) async throws {
        logger.info("启动代理抓包模式: \(proxyConfig.proxyURL)")

        // 检查当前状态，确保模式互斥
        let currentState = await stateManager.proxyState
        guard case .stopped = currentState else {
            throw UtunError.proxyFailed("当前已有模式在运行，请先停止: \(currentState)")
        }

        await updateProxyState(.starting)

        do {
            // 1. 创建和配置 UTUN 设备
            try await createUtun()
            try await configureUtun(localAddr: "********", remoteAddr: "********")

            // 2. 设置路由（根据 globalProxy 决定）
            if proxyConfig.globalProxy {
                try await redirectAllTrafficToUtun()
            }

            // 3. 启动代理到本地 MITM，MITM 再转发到外部代理
            let mitmProxyUrl = "http://127.0.0.1:8080" // 本地 MITM 代理地址
            try await startProxyInternal(
                proxyUrl: mitmProxyUrl,
                mtu: 1500,
                dnsStrategy: Tun2proxyDns_OverTcp,
                verbosity: Tun2proxyVerbosity_Info
            )

            // 4. 配置 MITM 转发到外部代理
            // TODO: 通知 MITM 模块使用指定的外部代理
            logger.info("配置 MITM 转发到外部代理: \(proxyConfig.proxyURL)")

            // 5. 如果有远程 WebSocket 服务器，启动转发
            if let remoteWS = captureConfig.remoteWSServer, !remoteWS.isEmpty {
                // TODO: 启动 WebSocket 转发逻辑
                logger.info("配置远程 WebSocket 转发: \(remoteWS)")
            }

            await updateProxyState(.running(.proxyCapture))
            logger.info("代理抓包模式启动成功")

        } catch {
            await updateProxyState(.error(error.localizedDescription))
            logger.error("代理抓包模式启动失败: \(error)")
            throw error
        }
    }

    /// 停止当前模式
    func stopCurrentMode() async throws {
        let currentState = await stateManager.proxyState

        guard case .running(let mode) = currentState else {
            logger.info("当前没有运行的模式")
            return
        }

        logger.info("停止当前模式: \(mode)")
        await updateProxyState(.stopping)

        do {
            // 1. 停止代理服务
            await stopProxy()

            // 2. 恢复路由表
            if routeBackupCount > 0 {
                try await restoreRoutingTable()
            }

            // 3. 关闭 UTUN 设备
            await closeUtun()

            await updateProxyState(.stopped)
            logger.info("模式停止成功")

        } catch {
            await updateProxyState(.error(error.localizedDescription))
            logger.error("停止模式失败: \(error)")
            throw error
        }
    }

    /// 获取当前状态
    func getCurrentState() async -> ProxyState {
        return await stateManager.proxyState
    }

    /// 获取当前模式
    func getCurrentMode() async -> ProxyMode? {
        return await stateManager.currentMode
    }

    /// 获取启动时间
    func getStartTime() async -> Date? {
        return await stateManager.startTime
    }
}

// MARK: - 配置管理接口
extension UtunProxy {

    /// 设置路由白名单（持久）
    func setRouteWhitelist(_ list: [String]) async throws {
        var config = await stateManager.getPersistentConfig()
        config.routeWhitelist = list
        await stateManager.updatePersistentConfig(config)

        // 持久化到文件
        try configManager.saveConfig(config)
        logger.info("路由白名单已更新并持久化: \(list)")
    }

    /// 设置路由黑名单（持久）
    func setRouteBlacklist(_ list: [String]) async throws {
        var config = await stateManager.getPersistentConfig()
        config.routeBlacklist = list
        await stateManager.updatePersistentConfig(config)

        // 持久化到文件
        try configManager.saveConfig(config)
        logger.info("路由黑名单已更新并持久化: \(list)")
    }

    /// 设置代理白名单（持久）
    func setProxyWhitelist(_ list: [String]) async throws {
        var config = await stateManager.getPersistentConfig()
        config.proxyWhitelist = list
        await stateManager.updatePersistentConfig(config)

        // 持久化到文件
        try configManager.saveConfig(config)
        logger.info("代理白名单已更新并持久化: \(list)")
    }

    /// 设置代理黑名单（持久）
    func setProxyBlacklist(_ list: [String]) async throws {
        var config = await stateManager.getPersistentConfig()
        config.proxyBlacklist = list
        await stateManager.updatePersistentConfig(config)

        // 持久化到文件
        try configManager.saveConfig(config)
        logger.info("代理黑名单已更新并持久化: \(list)")
    }

    /// 获取当前白、黑配置（四个名单的信息）
    func getConfigurations() async -> (route: (whitelist: [String], blacklist: [String]),
                                      proxy: (whitelist: [String], blacklist: [String])) {
        let config = await stateManager.getPersistentConfig()
        return (
            route: (whitelist: config.routeWhitelist, blacklist: config.routeBlacklist),
            proxy: (whitelist: config.proxyWhitelist, blacklist: config.proxyBlacklist)
        )
    }
}

// MARK: - 兼容性接口 - 保持与现有代码的兼容性
extension UtunProxy {

    /// 兼容性：获取代理状态（旧接口）
    func getProxyStatus() async -> ProxyState {
        let state = await stateManager.proxyState
        return state
    }

    /// 获取 UTUN 状态信息（更新版本）
    func getUtunInfo() async -> UtunInfo {
        let utunStatus = await stateManager.utunStatus
        let proxyStatus = await getProxyStatus()

        return UtunInfo(
            status: utunStatus,
            fileDescriptor: utunDevice.fd,
            name: withUnsafePointer(to: &utunDevice.ifname.0) { ptr in
                String(cString: ptr)
            },
            proxyStatus: proxyStatus
        )
    }
}
