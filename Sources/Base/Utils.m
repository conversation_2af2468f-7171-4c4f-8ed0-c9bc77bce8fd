//
//  Utils.m
//  iAntsUT
//
//  Created by sun on 2024/12/17.
//

#import "Utils.h"
#import <sys/resource.h>
#import <sys/types.h>
#import <unistd.h>
#import <mach/mach.h>
#import <mach/task.h>
#import <mach/thread_policy.h>
#import <IOKit/IOKitLib.h>
#import "include/kern_memorystatus.h"

@implementation UtilsC

+ (BOOL)setMaxFileLimit {
    struct rlimit limit;
    
    // 获取当前限制
    if (getrlimit(RLIMIT_NOFILE, &limit) != 0) {
        NSLog(@"获取文件描述符限制失败");
        return NO;
    }
    
    // 设置为最大值
    limit.rlim_cur = limit.rlim_max;
    
    if (setrlimit(RLIMIT_NOFILE, &limit) != 0) {
        NSLog(@"设置文件描述符限制失败");
        return NO;
    }
    
    NSLog(@"文件描述符限制设置为: %llu", limit.rlim_cur);
    return YES;
}

+ (BOOL)setProcessLimit:(id)process limit:(int)limitMB {
    pid_t pid = -1;
    
    if ([process isKindOfClass:[NSString class]]) {
        // 通过进程名查找PID
        // 这里简化处理，实际应用中需要更复杂的进程查找逻辑
        pid = getpid(); // 暂时使用当前进程
    } else if ([process isKindOfClass:[NSNumber class]]) {
        pid = [(NSNumber *)process intValue];
    } else {
        return NO;
    }
    
    // 设置内存限制（这里是简化版本）
    int result = memorystatus_control(MEMORYSTATUS_CMD_SET_JETSAM_HIGH_WATER_MARK, 
                                     pid, 
                                     limitMB, 
                                     NULL, 
                                     0);
    
    return (result == 0);
}

+ (BOOL)setProcessPriority:(id)process priority:(int)priority {
    pid_t pid = -1;
    
    if ([process isKindOfClass:[NSString class]]) {
        // 通过进程名查找PID
        pid = getpid(); // 暂时使用当前进程
    } else if ([process isKindOfClass:[NSNumber class]]) {
        pid = [(NSNumber *)process intValue];
    } else {
        return NO;
    }
    
    // 设置进程优先级
    int result = memorystatus_control(MEMORYSTATUS_CMD_SET_PRIORITY_PROPERTIES,
                                     pid,
                                     priority,
                                     NULL,
                                     0);
    
    return (result == 0);
}

+ (NSString *)getSerialNumber {
    NSString *serialNumber = nil;
    
    io_service_t platformExpert = IOServiceGetMatchingService(kIOMasterPortDefault,
                                                              IOServiceMatching("IOPlatformExpertDevice"));
    
    if (platformExpert) {
        CFStringRef serialNumberRef = (CFStringRef)IORegistryEntryCreateCFProperty(platformExpert,
                                                                                   CFSTR(kIOPlatformSerialNumberKey),
                                                                                   kCFAllocatorDefault,
                                                                                   0);
        if (serialNumberRef) {
            serialNumber = (__bridge NSString *)serialNumberRef;
            CFRelease(serialNumberRef);
        }
        
        IOObjectRelease(platformExpert);
    }
    
    return serialNumber ?: @"Unknown";
}

@end
