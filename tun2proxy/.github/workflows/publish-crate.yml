name: Publish to crates.io

on:
  push:
    tags:
      - 'v*.*.*'
  workflow_dispatch:

jobs:
  publish:
    name: Publish to crates.io
    runs-on: ubuntu-latest
    permissions:
      id-token: write
    steps:
    - uses: actions/checkout@v4
    - uses: rust-lang/crates-io-auth-action@v1
      id: auth
    - run: cargo publish
      env:
        CARGO_REGISTRY_TOKEN: ${{ steps.auth.outputs.token }}
