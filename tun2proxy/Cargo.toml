[package]
name = "tun2proxy"
version = "0.7.13"
edition = "2024"
license = "MIT"
repository = "https://github.com/tun2proxy/tun2proxy"
homepage = "https://github.com/tun2proxy/tun2proxy"
authors = ["<PERSON><PERSON>chmidt", "ssrlive"]
description = "Tunnel interface to proxy"
readme = "README.md"
rust-version = "1.85"

[lib]
crate-type = ["staticlib", "cdylib", "lib"]

[[bin]]
name = "tun2proxy-bin"
path = "src/bin/main.rs"

[[bin]]
name = "udpgw-server"
path = "src/bin/udpgw_server.rs"
required-features = ["udpgw"]

[features]
default = ["udpgw"]
udpgw = []

[dependencies]
aes = "0.8"
aes-gcm = "0.10"
async-trait = "0.1"
base64easy = "0.1"
cfb-mode = "0.8"
chacha20poly1305 = "0.10"
cipher = "0.4"
chrono = "0.4"
clap = { version = "4", features = ["derive", "wrap_help", "color"] }
ctrlc2 = { version = "3.6.5", features = ["async", "termination"] }
digest_auth = "0.3"
dotenvy = "0.15"
env_logger = "0.11"
fnv = "1.0"
hashlink = "0.10"
hickory-proto = "0.25"
httparse = "1"
ipstack = { version = "0.4" }
log = { version = "0.4", features = ["std"] }
md-5 = "0.10"
mimalloc = { version = "0.1", default-features = false, optional = true }
percent-encoding = "2"
rand = "0.8"
sha3 = "0.10"
shlex = "1.3.0"
socks5-impl = { version = "0.7", default-features = false, features = [
    "tokio",
] }
thiserror = "2"
tokio = { version = "1", features = ["full"] }
tokio-util = "0.7"
tproxy-config = { version = "^7.0.2", default-features = false }
tun = { version = "0.8", features = ["async"] }
udp-stream = { version = "0.0.12", default-features = false }
unicase = "2"
url = "2"
uuid = { version = "1.0", features = ["v4"] }

[target.'cfg(target_os="android")'.dependencies]
android_logger = "0.15"
jni = { version = "0.21", default-features = false }

[target.'cfg(target_os="linux")'.dependencies]
bincode = "2"
serde = { version = "1", features = ["derive"] }

[target.'cfg(target_os="windows")'.dependencies]
windows-service = "0.8"

[target.'cfg(unix)'.dependencies]
daemonize = "0.5"
nix = { version = "0.30", default-features = false, features = [
    "fs",
    "socket",
    "uio",
] }

[build-dependencies]
chrono = "0.4"
serde_json = "1"

# [profile.release]
# strip = "symbols"
