# iAnts MITM API 文档

本文档描述了 iAnts MITM 代理工具的内部 API 和插件开发接口。

## 核心模块 API

### Config 模块

#### 类型定义

```go
type Config struct {
    // 服务器配置
    Port         string
    EnableHTTPS  bool
    EnableSOCKS5 bool
    Timeout      time.Duration

    // 数据存储配置
    DataSavePath    string
    SaveInterval    int
    MaxBufferSize   int
    EnableLocalSave bool

    // 上传配置
    EnableUpload bool
    UploadURL    string
    UploadToken  string

    // 证书配置
    CertPath    string
    KeyPath     string
    AutoGenCert bool

    // 过滤配置
    BlockedHosts []string
    AllowedHosts []string

    // 性能配置
    MaxBodySize       int64
    MaxConcurrent     int
    EnableCompression bool

    // 调试配置
    Verbose     bool
    LogLevel    string
    EnableDebug bool
}
```

#### 函数

```go
// LoadConfig 从命令行参数加载配置
func LoadConfig() *Config

// ShowVersionInfo 显示版本信息
func ShowVersionInfo()

// Validate 验证配置
func (c *Config) Validate() error
```

### Interceptor 模块

#### 类型定义

```go
type InterceptedData struct {
    ID        string            `json:"id"`
    Timestamp time.Time         `json:"timestamp"`
    Method    string            `json:"method"`
    URL       string            `json:"url"`
    Headers   map[string]string `json:"headers"`
    Body      string            `json:"body,omitempty"`
    Response  *ResponseData     `json:"response,omitempty"`
    Protocol  string            `json:"protocol"`
    Size      int64             `json:"size"`
    Duration  time.Duration     `json:"duration,omitempty"`
}

type ResponseData struct {
    StatusCode int               `json:"status_code"`
    Headers    map[string]string `json:"headers"`
    Body       string            `json:"body,omitempty"`
    Size       int64             `json:"size"`
}

type Interceptor struct {
    // 私有字段
}
```

#### 函数

```go
// NewInterceptor 创建新的拦截器
func NewInterceptor(cfg *config.Config) *Interceptor

// GetDataChannel 获取数据通道
func (i *Interceptor) GetDataChannel() <-chan InterceptedData

// HandleHTTPRequest 处理HTTP请求
func (i *Interceptor) HandleHTTPRequest(req *http.Request, ctx *goproxy.ProxyCtx) (*http.Request, *http.Response)

// HandleHTTPResponse 处理HTTP响应
func (i *Interceptor) HandleHTTPResponse(resp *http.Response, ctx *goproxy.ProxyCtx) *http.Response

// HandleWebSocketConnect 处理WebSocket连接
func (i *Interceptor) HandleWebSocketConnect(host string, ctx *goproxy.ProxyCtx) (*goproxy.ConnectAction, string)
```

### Storage 模块

#### 类型定义

```go
type Storage struct {
    // 私有字段
}
```

#### 函数

```go
// NewStorage 创建新的存储管理器
func NewStorage(cfg *config.Config) *Storage

// Start 启动存储服务
func (s *Storage) Start(dataChannel <-chan interceptor.InterceptedData)

// Stop 停止存储服务
func (s *Storage) Stop()
```

### Proxy 模块

#### 类型定义

```go
type Server struct {
    // 私有字段
}
```

#### 函数

```go
// NewServer 创建新的代理服务器
func NewServer(cfg *config.Config) *Server

// Start 启动代理服务器
func (s *Server) Start() error

// Stop 停止代理服务器
func (s *Server) Stop()

// GetPluginManager 获取插件管理器
func (s *Server) GetPluginManager() *plugin.Manager
```

### Cert 模块

#### 类型定义

```go
type CertManager struct {
    // 私有字段
}
```

#### 函数

```go
// NewCertManager 创建证书管理器
func NewCertManager(certPath, keyPath string) *CertManager

// Initialize 初始化证书管理器
func (cm *CertManager) Initialize() error

// GetCertificateInfo 获取证书信息
func (cm *CertManager) GetCertificateInfo() (string, string)

// ExportCertificatePEM 导出证书PEM格式
func (cm *CertManager) ExportCertificatePEM() (string, error)

// PrintInstallInstructions 打印证书安装说明
func (cm *CertManager) PrintInstallInstructions()
```

### SOCKS5 模块

#### 类型定义

```go
type Server struct {
    // 私有字段
}
```

#### 函数

```go
// NewServer 创建SOCKS5服务器
func NewServer(cfg *config.Config) *Server

// Start 启动SOCKS5服务器
func (s *Server) Start() error

// Stop 停止SOCKS5服务器
func (s *Server) Stop()
```

## 插件系统 API

### 插件接口

#### 基础插件接口

```go
type Plugin interface {
    // GetInfo 获取插件信息
    GetInfo() PluginInfo

    // Initialize 初始化插件
    Initialize() error

    // Cleanup 清理插件资源
    Cleanup() error
}
```

#### 请求过滤器插件

```go
type RequestFilterPlugin interface {
    Plugin

    // FilterRequest 过滤请求
    FilterRequest(req *http.Request, ctx *goproxy.ProxyCtx) bool

    // ModifyRequest 修改请求
    ModifyRequest(req *http.Request, ctx *goproxy.ProxyCtx) *http.Request
}
```

#### 响应过滤器插件

```go
type ResponseFilterPlugin interface {
    Plugin

    // FilterResponse 过滤响应
    FilterResponse(resp *http.Response, ctx *goproxy.ProxyCtx) bool

    // ModifyResponse 修改响应
    ModifyResponse(resp *http.Response, ctx *goproxy.ProxyCtx) *http.Response
}
```

#### 数据处理器插件

```go
type DataProcessorPlugin interface {
    Plugin

    // ProcessData 处理拦截的数据
    ProcessData(data *interceptor.InterceptedData) error

    // GetProcessedData 获取处理后的数据
    GetProcessedData() []interceptor.InterceptedData
}
```

#### 连接处理器插件

```go
type ConnectHandlerPlugin interface {
    Plugin

    // HandleConnect 处理CONNECT请求
    HandleConnect(host string, ctx *goproxy.ProxyCtx) (*goproxy.ConnectAction, string)
}
```

#### 自定义插件

```go
type CustomPlugin interface {
    Plugin

    // Execute 执行自定义逻辑
    Execute(args map[string]interface{}) (map[string]interface{}, error)

    // GetCommands 获取支持的命令列表
    GetCommands() []string
}
```

### 插件管理器

#### 类型定义

```go
type Manager struct {
    // 私有字段
}

type PluginInfo struct {
    Name        string     `json:"name"`
    Version     string     `json:"version"`
    Author      string     `json:"author"`
    Description string     `json:"description"`
    Type        PluginType `json:"type"`
    Enabled     bool       `json:"enabled"`
}

type PluginContext struct {
    Config   map[string]interface{} `json:"config"`
    DataPath string                 `json:"data_path"`
    LogLevel string                 `json:"log_level"`
    UserData map[string]interface{} `json:"user_data"`
}
```

#### 函数

```go
// NewManager 创建插件管理器
func NewManager(ctx *PluginContext) *Manager

// RegisterPlugin 注册插件
func (m *Manager) RegisterPlugin(plugin Plugin) error

// UnregisterPlugin 注销插件
func (m *Manager) UnregisterPlugin(name string) error

// ProcessRequest 处理请求
func (m *Manager) ProcessRequest(req *http.Request, ctx *goproxy.ProxyCtx) (*http.Request, *http.Response)

// ProcessResponse 处理响应
func (m *Manager) ProcessResponse(resp *http.Response, ctx *goproxy.ProxyCtx) *http.Response

// ProcessData 处理数据
func (m *Manager) ProcessData(data *interceptor.InterceptedData)

// ProcessConnect 处理连接
func (m *Manager) ProcessConnect(host string, ctx *goproxy.ProxyCtx) (*goproxy.ConnectAction, string)

// ExecuteCustomCommand 执行自定义命令
func (m *Manager) ExecuteCustomCommand(pluginName, command string, args map[string]interface{}) (map[string]interface{}, error)

// EmitEvent 发送事件
func (m *Manager) EmitEvent(event PluginEvent)

// GetPlugins 获取所有插件信息
func (m *Manager) GetPlugins() map[string]PluginInfo

// EnablePlugin 启用插件
func (m *Manager) EnablePlugin(name string) error

// DisablePlugin 禁用插件
func (m *Manager) DisablePlugin(name string) error

// SetEnabled 设置插件管理器启用状态
func (m *Manager) SetEnabled(enabled bool)

// IsEnabled 检查插件管理器是否启用
func (m *Manager) IsEnabled() bool

// Cleanup 清理所有插件
func (m *Manager) Cleanup()
```

## 插件开发指南

### 创建基础插件

```go
package main

import (
    "iantsMitm/internal/plugin"
)

type MyPlugin struct {
    info plugin.PluginInfo
}

func NewMyPlugin() *MyPlugin {
    return &MyPlugin{
        info: plugin.PluginInfo{
            Name:        "MyPlugin",
            Version:     "1.0.0",
            Author:      "Your Name",
            Description: "My custom plugin description",
            Type:        plugin.PluginTypeRequestFilter,
            Enabled:     true,
        },
    }
}

func (p *MyPlugin) GetInfo() plugin.PluginInfo {
    return p.info
}

func (p *MyPlugin) Initialize() error {
    // 插件初始化逻辑
    log.Printf("Initializing plugin: %s", p.info.Name)
    return nil
}

func (p *MyPlugin) Cleanup() error {
    // 插件清理逻辑
    log.Printf("Cleaning up plugin: %s", p.info.Name)
    return nil
}
```

### 请求过滤器插件示例

```go
func (p *MyPlugin) FilterRequest(req *http.Request, ctx *goproxy.ProxyCtx) bool {
    // 阻止访问特定域名
    if strings.Contains(req.URL.Host, "blocked.com") {
        log.Printf("Blocking request to: %s", req.URL.Host)
        return false
    }
    return true
}

func (p *MyPlugin) ModifyRequest(req *http.Request, ctx *goproxy.ProxyCtx) *http.Request {
    // 添加自定义请求头
    req.Header.Set("X-Plugin-Processed", p.info.Name)
    req.Header.Set("X-Plugin-Version", p.info.Version)
    return req
}
```

### 响应过滤器插件示例

```go
func (p *MyPlugin) FilterResponse(resp *http.Response, ctx *goproxy.ProxyCtx) bool {
    // 阻止特定类型的响应
    contentType := resp.Header.Get("Content-Type")
    if strings.Contains(contentType, "application/octet-stream") {
        log.Printf("Blocking binary response from: %s", ctx.Req.URL.Host)
        return false
    }
    return true
}

func (p *MyPlugin) ModifyResponse(resp *http.Response, ctx *goproxy.ProxyCtx) *http.Response {
    // 添加自定义响应头
    resp.Header.Set("X-Plugin-Processed", p.info.Name)
    return resp
}
```

### 数据处理器插件示例

```go
type MyDataProcessor struct {
    plugin.Plugin
    processedData []interceptor.InterceptedData
    mutex         sync.RWMutex
}

func (p *MyDataProcessor) ProcessData(data *interceptor.InterceptedData) error {
    p.mutex.Lock()
    defer p.mutex.Unlock()

    // 处理数据逻辑
    if data.Protocol == "HTTPS" {
        // 只处理HTTPS数据
        processedData := *data
        processedData.Headers["X-Processed-By"] = p.info.Name

        p.processedData = append(p.processedData, processedData)

        // 限制存储数量
        if len(p.processedData) > 1000 {
            p.processedData = p.processedData[1:]
        }
    }

    return nil
}

func (p *MyDataProcessor) GetProcessedData() []interceptor.InterceptedData {
    p.mutex.RLock()
    defer p.mutex.RUnlock()

    result := make([]interceptor.InterceptedData, len(p.processedData))
    copy(result, p.processedData)
    return result
}
```

### 自定义插件示例

```go
func (p *MyPlugin) Execute(args map[string]interface{}) (map[string]interface{}, error) {
    command, ok := args["command"].(string)
    if !ok {
        return nil, fmt.Errorf("missing command parameter")
    }

    result := make(map[string]interface{})

    switch command {
    case "status":
        result["status"] = "running"
        result["plugin"] = p.info.Name
        result["version"] = p.info.Version

    case "stats":
        result["requests_processed"] = p.getRequestCount()
        result["responses_processed"] = p.getResponseCount()
        result["uptime"] = p.getUptime()

    case "config":
        result["config"] = p.getConfig()

    default:
        return nil, fmt.Errorf("unsupported command: %s", command)
    }

    return result, nil
}

func (p *MyPlugin) GetCommands() []string {
    return []string{"status", "stats", "config"}
}
```

## 事件系统

### 事件类型

```go
type PluginEvent struct {
    Type      string                 `json:"type"`
    Timestamp int64                  `json:"timestamp"`
    Data      map[string]interface{} `json:"data"`
}
```

### 事件处理器

```go
type EventHandler interface {
    HandleEvent(event PluginEvent) error
    GetSupportedEvents() []string
}
```

### 事件处理示例

```go
func (p *MyPlugin) HandleEvent(event PluginEvent) error {
    switch event.Type {
    case "request_intercepted":
        log.Printf("Request intercepted: %v", event.Data)

    case "response_intercepted":
        log.Printf("Response intercepted: %v", event.Data)

    case "connection_established":
        log.Printf("Connection established: %v", event.Data)

    default:
        log.Printf("Unknown event type: %s", event.Type)
    }

    return nil
}

func (p *MyPlugin) GetSupportedEvents() []string {
    return []string{
        "request_intercepted",
        "response_intercepted",
        "connection_established",
    }
}
```

## 错误处理

### 错误类型

插件开发中常见的错误类型：

- `ErrPluginNotFound`: 插件未找到
- `ErrPluginAlreadyExists`: 插件已存在
- `ErrPluginInitFailed`: 插件初始化失败
- `ErrInvalidCommand`: 无效命令
- `ErrPermissionDenied`: 权限不足

### 错误处理最佳实践

```go
func (p *MyPlugin) Initialize() error {
    // 检查依赖
    if !p.checkDependencies() {
        return fmt.Errorf("plugin dependencies not met")
    }

    // 初始化资源
    if err := p.initializeResources(); err != nil {
        return fmt.Errorf("failed to initialize resources: %w", err)
    }

    return nil
}

func (p *MyPlugin) ProcessData(data *interceptor.InterceptedData) error {
    defer func() {
        if r := recover(); r != nil {
            log.Printf("Plugin %s panic: %v", p.info.Name, r)
        }
    }()

    // 数据处理逻辑
    if err := p.processDataInternal(data); err != nil {
        return fmt.Errorf("data processing failed: %w", err)
    }

    return nil
}
```

## 性能优化

### 插件性能建议

1. **避免阻塞操作**: 在插件中避免长时间的阻塞操作
2. **使用协程**: 对于耗时操作，使用协程异步处理
3. **内存管理**: 及时释放不需要的资源
4. **缓存机制**: 合理使用缓存提高性能

### 示例优化

```go
type OptimizedPlugin struct {
    plugin.Plugin
    cache    sync.Map
    workers  chan struct{}
    stopChan chan struct{}
}

func (p *OptimizedPlugin) Initialize() error {
    // 限制并发工作协程数量
    p.workers = make(chan struct{}, 10)
    p.stopChan = make(chan struct{})

    return nil
}

func (p *OptimizedPlugin) ProcessData(data *interceptor.InterceptedData) error {
    // 非阻塞处理
    select {
    case p.workers <- struct{}{}:
        go func() {
            defer func() { <-p.workers }()
            p.processDataAsync(data)
        }()
    default:
        // 工作协程已满，跳过处理
        log.Printf("Plugin %s: worker pool full, skipping data", p.info.Name)
    }

    return nil
}

func (p *OptimizedPlugin) processDataAsync(data *interceptor.InterceptedData) {
    // 检查缓存
    if cached, ok := p.cache.Load(data.URL); ok {
        log.Printf("Using cached result for: %s", data.URL)
        return
    }

    // 处理数据
    result := p.expensiveProcessing(data)

    // 存储到缓存
    p.cache.Store(data.URL, result)
}
```

这个 API 文档提供了完整的插件开发接口说明和最佳实践，开发者可以根据这些接口创建自定义插件来扩展代理工具的功能。
```