package cert

import (
	"crypto/rand"
	"crypto/rsa"
	"crypto/tls"
	"crypto/x509"
	"crypto/x509/pkix"
	"encoding/pem"
	"fmt"
	"log"
	"math/big"
	"os"
	"path/filepath"
	"strings"
	"time"

	"github.com/elazarl/goproxy"
)

// CertManager 证书管理器
type CertManager struct {
	certPath string
	keyPath  string
	caCert   *x509.Certificate
	caKey    *rsa.PrivateKey
}

// NewCertManager 创建证书管理器
func NewCertManager(certPath, keyPath string) *CertManager {
	return &CertManager{
		certPath: certPath,
		keyPath:  keyPath,
	}
}

// Initialize 初始化证书管理器
func (cm *CertManager) Initialize() error {
	// 检查证书文件是否存在
	if cm.certificateExists() {
		log.Println("🔑 发现现有CA证书，正在加载...")
		return cm.loadExistingCertificate()
	}

	log.Println("🔑 未发现CA证书，正在生成新证书...")
	return cm.generateCACertificate()
}

// certificateExists 检查证书文件是否存在
func (cm *CertManager) certificateExists() bool {
	_, certErr := os.Stat(cm.certPath)
	_, keyErr := os.Stat(cm.keyPath)
	return certErr == nil && keyErr == nil
}

// loadExistingCertificate 加载现有证书
func (cm *CertManager) loadExistingCertificate() error {
	// 加载证书文件
	certPEM, err := os.ReadFile(cm.certPath)
	if err != nil {
		return fmt.Errorf("读取证书文件失败: %v", err)
	}

	keyPEM, err := os.ReadFile(cm.keyPath)
	if err != nil {
		return fmt.Errorf("读取私钥文件失败: %v", err)
	}

	// 解析证书
	certBlock, _ := pem.Decode(certPEM)
	if certBlock == nil {
		return fmt.Errorf("解析证书PEM失败")
	}

	cert, err := x509.ParseCertificate(certBlock.Bytes)
	if err != nil {
		return fmt.Errorf("解析证书失败: %v", err)
	}

	// 解析私钥
	keyBlock, _ := pem.Decode(keyPEM)
	if keyBlock == nil {
		return fmt.Errorf("解析私钥PEM失败")
	}

	key, err := x509.ParsePKCS1PrivateKey(keyBlock.Bytes)
	if err != nil {
		return fmt.Errorf("解析私钥失败: %v", err)
	}

	cm.caCert = cert
	cm.caKey = key

	// 创建tls.Certificate并设置goproxy的CA
	tlsCert := tls.Certificate{
		Certificate: [][]byte{cert.Raw},
		PrivateKey:  key,
	}
	goproxy.GoproxyCa = tlsCert

	log.Printf("✅ CA证书加载成功")
	log.Printf("   证书主题: %s", cert.Subject.CommonName)
	log.Printf("   有效期: %s 至 %s", cert.NotBefore.Format("2006-01-02"), cert.NotAfter.Format("2006-01-02"))

	return nil
}

// generateCACertificate 生成CA证书
func (cm *CertManager) generateCACertificate() error {
	// 创建证书目录
	certDir := filepath.Dir(cm.certPath)
	if err := os.MkdirAll(certDir, 0755); err != nil {
		return fmt.Errorf("创建证书目录失败: %v", err)
	}

	// 生成私钥
	log.Println("🔐 正在生成RSA私钥...")
	privateKey, err := rsa.GenerateKey(rand.Reader, 2048)
	if err != nil {
		return fmt.Errorf("生成私钥失败: %v", err)
	}

	// 创建证书模板
	template := &x509.Certificate{
		SerialNumber: big.NewInt(1),
		Subject: pkix.Name{
			Organization:       []string{"iAnts MITM Proxy"},
			OrganizationalUnit: []string{"Certificate Authority"},
			Country:            []string{"CN"},
			Province:           []string{"Beijing"},
			Locality:           []string{"Beijing"},
			CommonName:         "iAnts MITM CA",
		},
		NotBefore:             time.Now(),
		NotAfter:              time.Now().AddDate(10, 0, 0), // 10年有效期
		KeyUsage:              x509.KeyUsageKeyEncipherment | x509.KeyUsageDigitalSignature | x509.KeyUsageCertSign,
		ExtKeyUsage:           []x509.ExtKeyUsage{x509.ExtKeyUsageServerAuth, x509.ExtKeyUsageClientAuth},
		BasicConstraintsValid: true,
		IsCA:                  true,
		MaxPathLen:            0,
		MaxPathLenZero:        true,
	}

	// 生成证书
	log.Println("📜 正在生成CA证书...")
	certDER, err := x509.CreateCertificate(rand.Reader, template, template, &privateKey.PublicKey, privateKey)
	if err != nil {
		return fmt.Errorf("生成证书失败: %v", err)
	}

	// 解析生成的证书
	cert, err := x509.ParseCertificate(certDER)
	if err != nil {
		return fmt.Errorf("解析生成的证书失败: %v", err)
	}

	// 保存证书到文件
	if err := cm.saveCertificateToFile(certDER, privateKey); err != nil {
		return err
	}

	// 设置内部变量
	cm.caCert = cert
	cm.caKey = privateKey

	// 创建tls.Certificate并设置goproxy的CA
	tlsCert := tls.Certificate{
		Certificate: [][]byte{cert.Raw},
		PrivateKey:  privateKey,
	}
	goproxy.GoproxyCa = tlsCert

	log.Printf("✅ CA证书生成成功")
	log.Printf("   证书路径: %s", cm.certPath)
	log.Printf("   私钥路径: %s", cm.keyPath)
	log.Printf("   证书主题: %s", cert.Subject.CommonName)
	log.Printf("   有效期: %s 至 %s", cert.NotBefore.Format("2025-01-02"), cert.NotAfter.Format("2099-01-02"))

	return nil
}

// saveCertificateToFile 保存证书到文件
func (cm *CertManager) saveCertificateToFile(certDER []byte, privateKey *rsa.PrivateKey) error {
	// 保存证书文件
	certFile, err := os.Create(cm.certPath)
	if err != nil {
		return fmt.Errorf("创建证书文件失败: %v", err)
	}
	defer certFile.Close()

	if err := pem.Encode(certFile, &pem.Block{
		Type:  "CERTIFICATE",
		Bytes: certDER,
	}); err != nil {
		return fmt.Errorf("写入证书文件失败: %v", err)
	}

	// 保存私钥文件
	keyFile, err := os.Create(cm.keyPath)
	if err != nil {
		return fmt.Errorf("创建私钥文件失败: %v", err)
	}
	defer keyFile.Close()

	if err := pem.Encode(keyFile, &pem.Block{
		Type:  "RSA PRIVATE KEY",
		Bytes: x509.MarshalPKCS1PrivateKey(privateKey),
	}); err != nil {
		return fmt.Errorf("写入私钥文件失败: %v", err)
	}

	return nil
}

// GetCertificateInfo 获取证书信息
func (cm *CertManager) GetCertificateInfo() (string, string) {
	if cm.caCert == nil {
		return "", ""
	}

	return cm.certPath, cm.keyPath
}

// ExportCertificatePEM 导出证书PEM格式
func (cm *CertManager) ExportCertificatePEM() (string, error) {
	if cm.caCert == nil {
		return "", fmt.Errorf("证书未初始化")
	}

	certPEM := &pem.Block{
		Type:  "CERTIFICATE",
		Bytes: cm.caCert.Raw,
	}

	return string(pem.EncodeToMemory(certPEM)), nil
}

// PrintInstallInstructions 打印证书安装说明
func (cm *CertManager) PrintInstallInstructions() {
	if cm.caCert == nil {
		log.Println("⚠️ 证书未初始化")
		return
	}

	fmt.Println()
	fmt.Println("📖 CA证书安装说明:")
	fmt.Println("=" + strings.Repeat("=", 49))
	fmt.Printf("📁 证书文件路径: %s\n", cm.certPath)
	fmt.Println()
	fmt.Println("🍎 iOS设备安装步骤:")
	fmt.Println("1. 将证书文件传输到iOS设备")
	fmt.Println("2. 在iOS设备上打开证书文件")
	fmt.Println("3. 按照提示安装描述文件")
	fmt.Println("4. 前往 设置 > 通用 > 关于本机 > 证书信任设置")
	fmt.Println("5. 启用对 'iAnts MITM CA' 的完全信任")
	fmt.Println()
	fmt.Println("🖥️ macOS安装步骤:")
	fmt.Println("1. 双击证书文件打开钥匙串访问")
	fmt.Println("2. 将证书拖拽到'系统'钥匙串")
	fmt.Println("3. 双击证书，展开'信任'部分")
	fmt.Println("4. 将'使用此证书时'设置为'始终信任'")
	fmt.Println()
	fmt.Println("🐧 Linux安装步骤:")
	fmt.Printf("sudo cp %s /usr/local/share/ca-certificates/iants-mitm-ca.crt\n", cm.certPath)
	fmt.Println("sudo update-ca-certificates")
	fmt.Println()
	fmt.Println("=" + strings.Repeat("=", 49))
}
