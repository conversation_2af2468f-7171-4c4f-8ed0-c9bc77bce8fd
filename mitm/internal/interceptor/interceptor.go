package interceptor

import (
	"bytes"
	"io"
	"log"
	"net/http"
	"regexp"
	"strings"
	"sync"
	"time"

	"iantsMitm/internal/config"

	"github.com/elazarl/goproxy"
)

// InterceptedData 拦截的数据包结构
type InterceptedData struct {
	ID        string            `json:"id"`
	Timestamp time.Time         `json:"timestamp"`
	Method    string            `json:"method"`
	URL       string            `json:"url"`
	Headers   map[string]string `json:"headers"`
	Body      string            `json:"body,omitempty"`
	Response  *ResponseData     `json:"response,omitempty"`
	Protocol  string            `json:"protocol"` // HTTP/HTTPS/WebSocket/WSS
	Size      int64             `json:"size"`
	Duration  time.Duration     `json:"duration,omitempty"`
}

// ResponseData 响应数据结构
type ResponseData struct {
	StatusCode int               `json:"status_code"`
	Headers    map[string]string `json:"headers"`
	Body       string            `json:"body,omitempty"`
	Size       int64             `json:"size"`
}

// Interceptor 流量拦截器
type Interceptor struct {
	config       *config.Config
	dataChannel  chan InterceptedData
	blockedHosts []*regexp.Regexp
	// 请求-响应匹配映射
	pendingRequests sync.Map // map[string]*InterceptedData
	// 异步处理工作池
	workerPool chan struct{}
}

// NewInterceptor 创建新的拦截器
func NewInterceptor(cfg *config.Config) *Interceptor {
	interceptor := &Interceptor{
		config:      cfg,
		dataChannel: make(chan InterceptedData, cfg.MaxBufferSize),
		// 创建工作池，限制并发数
		workerPool: make(chan struct{}, cfg.MaxConcurrent/2), // 使用一半的并发数用于异步处理
	}

	// 编译阻止主机的正则表达式
	for _, host := range cfg.BlockedHosts {
		if regex, err := regexp.Compile(host); err == nil {
			interceptor.blockedHosts = append(interceptor.blockedHosts, regex)
		}
	}

	return interceptor
}

// GetDataChannel 获取数据通道
func (i *Interceptor) GetDataChannel() <-chan InterceptedData {
	return i.dataChannel
}

// HandleHTTPRequest 处理HTTP请求
func (i *Interceptor) HandleHTTPRequest(req *http.Request, ctx *goproxy.ProxyCtx) (*http.Request, *http.Response) {
	// 检查是否被阻止
	if i.isBlocked(req.URL.Host) {
		log.Printf("🚫 阻止访问: %s", req.URL.Host)
		return req, goproxy.NewResponse(req, goproxy.ContentTypeText,
			http.StatusForbidden, "🚫 访问被阻止")
	}

	// 检查请求体大小限制
	if req.ContentLength > i.config.MaxBodySize {
		log.Printf("⚠️ 请求体过大: %d bytes", req.ContentLength)
		return req, goproxy.NewResponse(req, goproxy.ContentTypeText,
			http.StatusRequestEntityTooLarge, "请求体过大")
	}

	startTime := time.Now()
	requestID := generateID()

	// 创建拦截数据
	interceptedData := &InterceptedData{
		ID:        requestID,
		Timestamp: startTime,
		Method:    req.Method,
		URL:       req.URL.String(),
		Headers:   i.flattenHeaders(req.Header),
		Protocol:  i.getProtocol(req),
		Size:      req.ContentLength,
	}

	// 异步处理请求体读取
	if req.Body != nil && req.ContentLength > 0 {
		// 先读取请求体
		body, err := io.ReadAll(req.Body)
		if err == nil {
			interceptedData.Body = string(body)
			// 重新创建请求体供后续使用
			req.Body = io.NopCloser(bytes.NewReader(body))
		}
	}

	// 将请求数据存储到待处理映射中，等待响应匹配
	i.pendingRequests.Store(requestID, interceptedData)

	// 将请求ID和开始时间存储在上下文中
	ctx.UserData = map[string]interface{}{
		"requestID": requestID,
		"startTime": startTime,
	}

	if i.config.Verbose {
		log.Printf("📨 拦截请求: %s %s", req.Method, req.URL.String())
	}

	return req, nil
}

// HandleHTTPResponse 处理HTTP响应
func (i *Interceptor) HandleHTTPResponse(resp *http.Response, ctx *goproxy.ProxyCtx) *http.Response {
	if resp == nil {
		return resp
	}

	// 从上下文获取请求信息
	var requestID string
	var startTime time.Time

	if userData, ok := ctx.UserData.(map[string]interface{}); ok {
		if id, exists := userData["requestID"]; exists {
			requestID = id.(string)
		}
		if st, exists := userData["startTime"]; exists {
			startTime = st.(time.Time)
		}
	}

	// 如果没有找到请求ID，跳过处理
	if requestID == "" {
		return resp
	}

	// 同步读取响应体（避免被消费）
	var responseBody string
	var responseSize int64

	if resp.Body != nil {
		bodyBytes, err := io.ReadAll(resp.Body)
		if err == nil {
			if int64(len(bodyBytes)) <= i.config.MaxBodySize {
				responseBody = string(bodyBytes)
			}
			responseSize = int64(len(bodyBytes))
			// 重新创建响应体供后续使用
			resp.Body = io.NopCloser(bytes.NewReader(bodyBytes))
		}
	}

	// 异步处理响应数据
	go i.processResponseAsync(requestID, resp, startTime, responseBody, responseSize)

	if i.config.Verbose {
		log.Printf("📬 拦截响应: %d %s", resp.StatusCode, ctx.Req.URL.String())
	}

	return resp
}

// processResponseAsync 异步处理响应数据
func (i *Interceptor) processResponseAsync(requestID string, resp *http.Response, startTime time.Time, responseBody string, responseSize int64) {
	// 获取工作池令牌，限制并发数
	select {
	case i.workerPool <- struct{}{}:
		defer func() { <-i.workerPool }()
	default:
		// 工作池已满，跳过处理
		log.Printf("⚠️ 工作池已满，跳过响应处理: %s", requestID)
		return
	}

	// 从待处理映射中获取请求数据
	if reqDataInterface, ok := i.pendingRequests.LoadAndDelete(requestID); ok {
		reqData := reqDataInterface.(*InterceptedData)

		// 计算持续时间
		duration := time.Since(startTime)

		// 创建响应数据
		responseData := &ResponseData{
			StatusCode: resp.StatusCode,
			Headers:    i.flattenHeaders(resp.Header),
			Body:       responseBody,
			Size:       responseSize,
		}

		// 将响应数据添加到请求数据中
		reqData.Response = responseData
		reqData.Duration = duration

		// 异步发送到数据通道
		select {
		case i.dataChannel <- *reqData:
		default:
			log.Printf("⚠️ 数据通道已满，丢弃完整数据: %s", reqData.URL)
		}
	}
}

// HandleWebSocketConnect 处理WebSocket连接
func (i *Interceptor) HandleWebSocketConnect(host string, ctx *goproxy.ProxyCtx) (*goproxy.ConnectAction, string) {
	// 检查是否是 WebSocket 升级请求
	if i.isWebSocketRequest(ctx.Req) {
		log.Printf("🔌 检测到 WebSocket 连接: %s", host)

		// 记录WebSocket连接
		interceptedData := InterceptedData{
			ID:        generateID(),
			Timestamp: time.Now(),
			Method:    "CONNECT",
			URL:       "ws://" + host,
			Headers:   i.flattenHeaders(ctx.Req.Header),
			Protocol:  i.getWebSocketProtocol(ctx.Req),
		}

		select {
		case i.dataChannel <- interceptedData:
		default:
			log.Printf("⚠️ 数据通道已满，丢弃WebSocket连接数据: %s", host)
		}

		return goproxy.MitmConnect, host
	}

	return goproxy.MitmConnect, host
}

// 辅助方法

// generateID 生成唯一ID
func generateID() string {
	return time.Now().Format("20060102150405") + "-" + randomString(6)
}

// randomString 生成随机字符串
func randomString(length int) string {
	const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
	b := make([]byte, length)
	for i := range b {
		b[i] = charset[time.Now().UnixNano()%int64(len(charset))]
	}
	return string(b)
}

// isBlocked 检查主机是否被阻止
func (i *Interceptor) isBlocked(host string) bool {
	for _, regex := range i.blockedHosts {
		if regex.MatchString(host) {
			return true
		}
	}
	return false
}

// flattenHeaders 扁平化HTTP头
func (i *Interceptor) flattenHeaders(headers http.Header) map[string]string {
	flat := make(map[string]string)
	for key, values := range headers {
		flat[key] = strings.Join(values, "; ")
	}
	return flat
}

// getProtocol 获取协议类型
func (i *Interceptor) getProtocol(req *http.Request) string {
	if req.TLS != nil {
		return "HTTPS"
	}
	if i.isWebSocketRequest(req) {
		if req.TLS != nil {
			return "WSS"
		}
		return "WebSocket"
	}
	return "HTTP"
}

// getWebSocketProtocol 获取WebSocket协议类型
func (i *Interceptor) getWebSocketProtocol(req *http.Request) string {
	if req.TLS != nil {
		return "WSS"
	}
	return "WebSocket"
}

// isWebSocketRequest 检查是否是WebSocket请求
func (i *Interceptor) isWebSocketRequest(req *http.Request) bool {
	return strings.ToLower(req.Header.Get("Upgrade")) == "websocket"
}

// CleanupPendingRequests 清理超时的待处理请求
func (i *Interceptor) CleanupPendingRequests() {
	cutoff := time.Now().Add(-30 * time.Second) // 30秒超时

	i.pendingRequests.Range(func(key, value interface{}) bool {
		reqData := value.(*InterceptedData)
		if reqData.Timestamp.Before(cutoff) {
			// 删除超时的请求
			i.pendingRequests.Delete(key)

			// 发送没有响应的请求数据
			select {
			case i.dataChannel <- *reqData:
			default:
				log.Printf("⚠️ 数据通道已满，丢弃超时请求: %s", reqData.URL)
			}
		}
		return true
	})
}
