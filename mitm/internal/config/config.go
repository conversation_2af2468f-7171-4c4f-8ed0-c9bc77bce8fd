package config

import (
	"flag"
	"fmt"
	"os"
	"runtime"
	"time"
)

// Config 代理服务器配置
type Config struct {
	// 服务器配置
	Port         string
	EnableHTTPS  bool
	EnableSOCKS5 bool
	Timeout      time.Duration

	// 数据存储配置
	DataSavePath    string
	SaveInterval    int // 秒
	MaxBufferSize   int
	EnableLocalSave bool

	// 上传配置
	EnableUpload bool
	UploadURL    string
	UploadToken  string

	// 证书配置
	CertPath    string
	KeyPath     string
	AutoGenCert bool

	// 过滤配置
	BlockedHosts []string
	AllowedHosts []string

	// 性能配置
	MaxBodySize       int64
	MaxConcurrent     int
	EnableCompression bool

	// 调试配置
	Verbose     bool
	LogLevel    string
	EnableDebug bool
}

// LoadConfig 从命令行参数加载配置
func LoadConfig() *Config {
	config := &Config{}

	// 版本信息标志
	var showVersion bool
	flag.BoolVar(&showVersion, "version", false, "显示版本信息")

	// 定义命令行参数
	flag.StringVar(&config.Port, "port", "8889", "代理服务器监听端口")
	flag.BoolVar(&config.EnableHTTPS, "https", true, "启用HTTPS MITM")
	flag.BoolVar(&config.EnableSOCKS5, "socks5", false, "启用SOCKS5代理")
	flag.StringVar(&config.DataSavePath, "data-path", "./intercepted_data/", "数据保存路径")
	flag.IntVar(&config.SaveInterval, "save-interval", 30, "数据保存间隔(秒)")
	flag.IntVar(&config.MaxBufferSize, "buffer-size", 1000, "最大缓冲区大小")
	flag.BoolVar(&config.EnableLocalSave, "local-save", true, "启用本地保存")
	flag.BoolVar(&config.EnableUpload, "upload", false, "启用数据上传")
	flag.StringVar(&config.UploadURL, "upload-url", "", "上传服务器URL")
	flag.StringVar(&config.UploadToken, "upload-token", "", "上传认证令牌")
	flag.StringVar(&config.CertPath, "cert-path", "./certs/ca-cert.pem", "CA证书路径")
	flag.StringVar(&config.KeyPath, "key-path", "./certs/ca-key.pem", "CA私钥路径")
	flag.BoolVar(&config.AutoGenCert, "auto-cert", true, "自动生成证书")
	flag.Int64Var(&config.MaxBodySize, "max-body", 10*1024*1024, "最大请求体大小(字节)")
	flag.IntVar(&config.MaxConcurrent, "max-concurrent", 100, "最大并发连接数")
	flag.BoolVar(&config.Verbose, "verbose", true, "详细日志输出")
	flag.BoolVar(&config.EnableDebug, "debug", false, "启用调试模式")

	// 解析命令行参数
	flag.Parse()

	// 处理版本信息显示
	if showVersion {
		ShowVersionInfo()
		os.Exit(0)
	}

	// 设置其他默认值
	config.Timeout = 30 * time.Second
	config.EnableCompression = true
	config.LogLevel = "info"
	config.BlockedHosts = []string{"ads\\..*", "tracker\\..*", "analytics\\..*"}
	config.AllowedHosts = []string{}

	return config
}

// ShowVersionInfo 显示版本信息
func ShowVersionInfo() {
	// 这些变量将在构建时通过 ldflags 注入
	version := getVersion()
	buildTime := getBuildTime()
	gitCommit := getGitCommit()

	fmt.Printf("iAnts MITM 代理工具\n")
	fmt.Printf("版本: %s\n", version)
	fmt.Printf("构建时间: %s\n", buildTime)
	fmt.Printf("Git 提交: %s\n", gitCommit)
	fmt.Printf("Go 版本: %s\n", runtime.Version())
	fmt.Printf("操作系统: %s/%s\n", runtime.GOOS, runtime.GOARCH)
}

// 这些函数将通过构建脚本的 ldflags 替换
var (
	version   = "dev"
	buildTime = "unknown"
	gitCommit = "unknown"
)

func getVersion() string   { return version }
func getBuildTime() string { return buildTime }
func getGitCommit() string { return gitCommit }

// Validate 验证配置
func (c *Config) Validate() error {
	// 创建必要的目录
	if c.EnableLocalSave {
		if err := os.MkdirAll(c.DataSavePath, 0755); err != nil {
			return err
		}
	}

	if c.AutoGenCert {
		certDir := "./certs"
		if err := os.MkdirAll(certDir, 0755); err != nil {
			return err
		}
	}

	return nil
}
