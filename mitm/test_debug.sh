#!/bin/bash

# 测试debug.sh脚本的各种功能

echo "=== 测试 debug.sh 脚本 ==="

echo "1. 测试帮助信息"
./debug.sh --help

echo -e "\n2. 测试依赖检查"
# 模拟没有sshpass的情况
if command -v sshpass &> /dev/null; then
    echo "✅ sshpass 已安装"
else
    echo "❌ sshpass 未安装，请运行: brew install sshpass"
fi

echo -e "\n3. 测试构建功能（仅编译，不上传）"
echo "编译 iOS debug 版本..."
./build.sh ios -s

if [ -f "bin/mitm-ios-arm64-debug" ]; then
    echo "✅ iOS debug 版本编译成功"
    ls -lh bin/mitm-ios-arm64-debug
    file bin/mitm-ios-arm64-debug
else
    echo "❌ iOS debug 版本编译失败"
fi

echo -e "\n4. 测试构建 release 版本"
./build.sh ios -r -s

if [ -f "bin/mitm-ios-arm64-release" ]; then
    echo "✅ iOS release 版本编译成功"
    ls -lh bin/mitm-ios-arm64-release
    file bin/mitm-ios-arm64-release
else
    echo "❌ iOS release 版本编译失败"
fi

echo -e "\n=== 测试完成 ==="
echo "如果要测试完整部署流程，请确保:"
echo "1. iOS设备已连接并开启SSH"
echo "2. 端口转发已设置 (如: iproxy 2222 22)"
echo "3. 然后运行: ./debug.sh"