# iAnts MITM iOS 调试部署指南

## 🚀 快速开始

### 1. 环境准备

```bash
# 安装必要工具
brew install sshpass libimobiledevice

# 验证安装
sshpass -V
iproxy --help
```

### 2. 设备准备

1. **iOS设备越狱** - 确保设备已越狱
2. **安装OpenSSH** - 在Cydia/Sileo中安装OpenSSH
3. **修改root密码** - 建议修改默认密码
4. **连接设备** - 通过USB连接设备到电脑

### 3. 端口转发

```bash
# 设置SSH端口转发 (2222 -> 22)
iproxy 2222 22

# 验证连接
ssh -p 2222 root@127.0.0.1
```

## 📋 使用方法

### 基本用法

```bash
# 编译debug版本并部署
./debug.sh

# 编译release版本并部署
./debug.sh -r

# 部署后启动调试会话
./debug.sh -s

# 仅测试设备连接
./debug.sh -t
```

### 高级用法

```bash
# 自定义SSH配置
./debug.sh -p 2223 -H ************* -u mobile

# 自定义设备路径
./debug.sh -d /var/mobile

# 使用自定义密码
./debug.sh -P your_password
```

## 🔧 脚本功能

### 自动化流程

1. **✅ 依赖检查** - 检查sshpass、scp等工具
2. **✅ 连接测试** - 验证SSH连接是否正常
3. **✅ 编译构建** - 自动编译iOS版本并签名
4. **✅ 清理旧文件** - 删除设备上的旧版本
5. **✅ 上传部署** - 上传新版本到设备
6. **✅ 权限设置** - 自动设置执行权限
7. **✅ 验证部署** - 验证文件是否正确上传

### 配置选项

| 选项 | 默认值 | 说明 |
|------|--------|------|
| `-r, --release` | debug | 构建release版本 |
| `-p, --port` | 2222 | SSH端口 |
| `-H, --host` | 127.0.0.1 | SSH主机 |
| `-u, --user` | root | SSH用户 |
| `-P, --password` | alpine | SSH密码 |
| `-d, --device-path` | /tmp | 设备路径 |
| `-s, --session` | false | 启动调试会话 |
| `-t, --test-only` | false | 仅测试连接 |

## 🐛 调试会话

使用 `-s` 参数可以在部署后自动启动调试会话：

```bash
./debug.sh -s
```

调试会话提供：
- 自动连接到设备
- 切换到二进制文件目录
- 显示使用说明
- 交互式shell环境

### 在设备上运行

```bash
# 基本启动
./mitm-ios-arm64-debug

# 指定端口
./mitm-ios-arm64-debug -port 8889

# 查看版本
./mitm-ios-arm64-debug -version

# 查看帮助
./mitm-ios-arm64-debug -help
```

## ⚠️ 常见问题

### 1. 连接失败

```bash
# 检查端口转发
ps aux | grep iproxy

# 重新设置端口转发
killall iproxy
iproxy 2222 22

# 测试连接
./debug.sh -t
```

### 2. 权限问题

```bash
# 检查SSH服务
ssh -p 2222 root@127.0.0.1 "ps aux | grep sshd"

# 重启SSH服务
ssh -p 2222 root@127.0.0.1 "launchctl unload /Library/LaunchDaemons/com.openssh.sshd.plist"
ssh -p 2222 root@127.0.0.1 "launchctl load /Library/LaunchDaemons/com.openssh.sshd.plist"
```

### 3. 编译失败

```bash
# 检查Go环境
go version

# 检查依赖
go mod tidy

# 手动编译
./build.sh ios -s
```

### 4. 代理循环问题

如果遇到代理循环，请确保：

1. **Shadowrocket配置**：
   ```
   # 添加规则
   PROCESS-NAME,mitm-ios-arm64-debug,DIRECT
   IP-CIDR,127.0.0.1/32,DIRECT
   ```

2. **使用正确端口**：
   ```bash
   ./mitm-ios-arm64-debug -port 8889
   ```

3. **检查代理设置**：
   - Shadowrocket: 127.0.0.1:8889
   - 确保MITM代理不经过Shadowrocket

## 📊 部署流程图

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   检查依赖      │───▶│   测试连接      │───▶│   编译构建      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                                        │
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   启动会话      │◀───│   验证部署      │◀───│   上传文件      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                                        ▲
                       ┌─────────────────┐              │
                       │   清理旧文件    │──────────────┘
                       └─────────────────┘
```

## 🎯 最佳实践

1. **定期更新** - 保持工具和依赖的最新版本
2. **备份配置** - 备份重要的配置文件
3. **监控日志** - 关注部署和运行日志
4. **测试验证** - 部署后进行功能验证
5. **安全考虑** - 修改默认密码，限制网络访问

## 📝 日志和监控

```bash
# 查看部署日志
./debug.sh -s 2>&1 | tee deploy.log

# 监控设备上的进程
ssh -p 2222 root@127.0.0.1 "ps aux | grep mitm"

# 查看网络连接
ssh -p 2222 root@127.0.0.1 "netstat -an | grep 8889"
```

---

**提示**: 如果遇到问题，请先运行 `./debug.sh -t` 测试连接，然后查看具体的错误信息。