#!/bin/bash

# iAnts MITM iOS 调试部署脚本
# 自动编译、删除旧文件、上传新文件到iOS设备
#
# 使用前准备:
# 1. 安装 sshpass: brew install sshpass
# 2. 安装 libimobiledevice: brew install libimobiledevice
# 3. 设置端口转发: iproxy 2222 22
# 4. 确保iOS设备已越狱并开启SSH
#
# 使用示例:
# ./debug.sh              # 编译debug版本并部署
# ./debug.sh -r           # 编译release版本并部署
# ./debug.sh -s           # 部署后启动调试会话
# ./debug.sh -t           # 仅测试连接

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 设备连接配置
SSH_HOST="127.0.0.1"
SSH_PORT="2222"
SSH_USER="root"
SSH_PASS="alpine"
DEVICE_PATH="/tmp"

# 构建配置
BINARY_NAME="mitm-ios-arm64-debug"
BUILD_TYPE="debug"  # debug 或 release

# 打印带颜色的消息
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查依赖
check_dependencies() {
    print_info "检查依赖工具..."

    # 检查 sshpass
    if ! command -v sshpass &> /dev/null; then
        print_error "sshpass 未安装"
        print_info "安装方法: brew install sshpass"
        exit 1
    fi

    # 检查 scp
    if ! command -v scp &> /dev/null; then
        print_error "scp 未安装"
        exit 1
    fi

    # 检查构建脚本
    if [ ! -f "./build.sh" ]; then
        print_error "build.sh 不存在"
        exit 1
    fi

    print_success "依赖检查完成"
}

# 测试设备连接
test_connection() {
    print_info "测试设备连接..."

    if sshpass -p "$SSH_PASS" ssh -p "$SSH_PORT" -o ConnectTimeout=5 -o StrictHostKeyChecking=no "$SSH_USER@$SSH_HOST" "echo 'Connection OK'" &> /dev/null; then
        print_success "设备连接正常"
    else
        print_error "无法连接到设备 $SSH_HOST:$SSH_PORT"
        print_info "请检查:"
        print_info "1. 设备是否开启SSH"
        print_info "2. 端口转发是否正确"
        print_info "3. 密码是否正确"
        exit 1
    fi
}

# 编译iOS版本
build_ios() {
    print_info "开始编译 iOS $BUILD_TYPE 版本..."

    local build_args="ios"
    if [ "$BUILD_TYPE" = "release" ]; then
        build_args="$build_args -r"
    fi
    build_args="$build_args -s"  # 总是签名

    if ./build.sh $build_args; then
        print_success "iOS 版本编译完成"
    else
        print_error "iOS 版本编译失败"
        exit 1
    fi

    # 检查二进制文件是否存在
    if [ ! -f "bin/$BINARY_NAME" ]; then
        print_error "编译后的二进制文件不存在: bin/$BINARY_NAME"
        exit 1
    fi

    # 显示文件信息
    print_info "二进制文件信息:"
    ls -lh "bin/$BINARY_NAME"
    file "bin/$BINARY_NAME"
}

# 删除设备上的旧文件
remove_old_binary() {
    print_info "删除设备上的旧文件..."

    # 检查文件是否存在并删除
    sshpass -p "$SSH_PASS" ssh -p "$SSH_PORT" -o StrictHostKeyChecking=no "$SSH_USER@$SSH_HOST" "
        if [ -f '$DEVICE_PATH/$BINARY_NAME' ]; then
            echo '删除旧文件: $DEVICE_PATH/$BINARY_NAME'
            rm -f '$DEVICE_PATH/$BINARY_NAME'
            echo '旧文件已删除'
        else
            echo '设备上没有旧文件'
        fi
    "

    print_success "旧文件清理完成"
}

# 上传新文件到设备
upload_binary() {
    print_info "上传新文件到设备..."

    # 上传文件
    if sshpass -p "$SSH_PASS" scp -P "$SSH_PORT" -o StrictHostKeyChecking=no "bin/$BINARY_NAME" "$SSH_USER@$SSH_HOST:$DEVICE_PATH/"; then
        print_success "文件上传完成"
    else
        print_error "文件上传失败"
        exit 1
    fi

    # 设置执行权限
    print_info "设置执行权限..."
    sshpass -p "$SSH_PASS" ssh -p "$SSH_PORT" -o StrictHostKeyChecking=no "$SSH_USER@$SSH_HOST" "
        chmod +x '$DEVICE_PATH/$BINARY_NAME'
        echo '执行权限已设置'
    "

    # 验证上传结果
    print_info "验证上传结果..."
    sshpass -p "$SSH_PASS" ssh -p "$SSH_PORT" -o StrictHostKeyChecking=no "$SSH_USER@$SSH_HOST" "
        if [ -f '$DEVICE_PATH/$BINARY_NAME' ]; then
            echo '文件存在: $DEVICE_PATH/$BINARY_NAME'
            ls -la '$DEVICE_PATH/$BINARY_NAME'
            file '$DEVICE_PATH/$BINARY_NAME'
        else
            echo '错误: 文件不存在'
            exit 1
        fi
    "

    print_success "文件验证完成"
}

# 启动远程调试会话
start_debug_session() {
    print_info "启动远程调试会话..."

    print_info "连接到设备进行调试..."
    print_info "设备路径: $DEVICE_PATH/$BINARY_NAME"
    print_info "使用 Ctrl+C 退出调试会话"

    # 连接到设备并进入交互模式
    sshpass -p "$SSH_PASS" ssh -p "$SSH_PORT" -o StrictHostKeyChecking=no "$SSH_USER@$SSH_HOST" -t "
        echo '=== iAnts MITM 调试会话 ==='
        echo '当前目录: \$(pwd)'
        echo '二进制文件: $DEVICE_PATH/$BINARY_NAME'
        echo '使用方法:'
        echo '  ./$BINARY_NAME                    # 启动代理'
        echo '  ./$BINARY_NAME -port 8889         # 指定端口'
        echo '  ./$BINARY_NAME -version           # 查看版本'
        echo '  ./$BINARY_NAME -help              # 查看帮助'
        echo '=================================='
        cd $DEVICE_PATH
        exec \$SHELL
    "
}

# 显示帮助信息
show_help() {
    cat << EOF
iAnts MITM iOS 调试部署脚本

用法: $0 [选项]

选项:
    -h, --help          显示此帮助信息
    -r, --release       构建 release 版本（默认: debug）
    -p, --port PORT     SSH 端口（默认: 2222）
    -H, --host HOST     SSH 主机（默认: 127.0.0.1）
    -u, --user USER     SSH 用户（默认: root）
    -P, --password PASS SSH 密码（默认: alpine）
    -d, --device-path   设备路径（默认: /tmp）
    -s, --session       部署后启动调试会话
    -t, --test-only     仅测试连接，不执行部署

示例:
    $0                          # 编译debug版本并部署
    $0 -r                       # 编译release版本并部署
    $0 -s                       # 部署后启动调试会话
    $0 -p 2223 -H ************* # 使用自定义SSH配置
    $0 -t                       # 仅测试设备连接

EOF
}

# 显示部署总结
show_summary() {
    echo
    print_success "========================================="
    print_success "部署完成！"
    print_success "========================================="
    print_info "设备信息:"
    print_info "  主机: $SSH_HOST:$SSH_PORT"
    print_info "  用户: $SSH_USER"
    print_info "  路径: $DEVICE_PATH/$BINARY_NAME"
    echo
    print_info "使用方法:"
    print_info "  ssh -p $SSH_PORT $SSH_USER@$SSH_HOST"
    print_info "  cd $DEVICE_PATH"
    print_info "  ./$BINARY_NAME"
    echo
    print_info "或者运行: $0 -s 启动调试会话"
    print_success "========================================="
}

# 主函数
main() {
    local start_session=false
    local test_only=false

    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -r|--release)
                BUILD_TYPE="release"
                BINARY_NAME="mitm-ios-arm64-release"
                shift
                ;;
            -p|--port)
                SSH_PORT="$2"
                shift 2
                ;;
            -H|--host)
                SSH_HOST="$2"
                shift 2
                ;;
            -u|--user)
                SSH_USER="$2"
                shift 2
                ;;
            -P|--password)
                SSH_PASS="$2"
                shift 2
                ;;
            -d|--device-path)
                DEVICE_PATH="$2"
                shift 2
                ;;
            -s|--session)
                start_session=true
                shift
                ;;
            -t|--test-only)
                test_only=true
                shift
                ;;
            *)
                print_error "未知选项: $1"
                echo "使用 $0 --help 查看帮助信息"
                exit 1
                ;;
        esac
    done

    # 显示配置信息
    echo
    print_info "========================================="
    print_info "iAnts MITM iOS 调试部署脚本"
    print_info "========================================="
    print_info "构建类型: $BUILD_TYPE"
    print_info "二进制文件: $BINARY_NAME"
    print_info "SSH 配置: $SSH_USER@$SSH_HOST:$SSH_PORT"
    print_info "设备路径: $DEVICE_PATH"
    print_info "启动会话: $start_session"
    print_info "========================================="
    echo

    # 检查依赖
    check_dependencies

    # 测试连接
    test_connection

    # 如果只是测试连接，直接退出
    if [ "$test_only" = true ]; then
        print_success "连接测试完成！"
        exit 0
    fi

    # 执行部署流程
    build_ios
    remove_old_binary
    upload_binary

    # 显示部署总结
    show_summary

    # 启动调试会话（如果需要）
    if [ "$start_session" = true ]; then
        start_debug_session
    fi
}

# 脚本入口点
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi