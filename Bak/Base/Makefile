INSTALL_TARGET_PROCESSES = utunT
THEOS_DEVICE_IP=127.0.0.1
THEOS_DEVICE_PORT=2222
FINALPACKAGE=1
DEBUG ?= 0

TARGET := iphone:clang:latest:15
ARCHS := arm64

THEOS_PACKAGE_SCHEME = roothide

include $(THEOS)/makefiles/common.mk

TOOL_NAME = utunT

$(TOOL_NAME)_FILES = main.m
$(TOOL_NAME)_CFLAGS = -fobjc-arc -I$(THEOS_PROJECT_DIR)/include
$(TOOL_NAME)_LDFLAGS = -L../../libs -ltun2proxy
$(TOOL_NAME)_CODESIGN_FLAGS = -S../../entitlements.plist
$(TOOL_NAME)_INSTALL_PATH = /usr/bin

ifeq ($(DEBUG), 1)
    $(TOOL_NAME)_CFLAGS += -g -O0 -DDEBUG
    $(TOOL_NAME)_LDFLAGS += -g
endif

include $(THEOS_MAKE_PATH)/tool.mk