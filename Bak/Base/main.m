// 禁用所有模块以避免头文件冲突
#ifdef __clang__
#pragma clang diagnostic ignored "-Weverything"
#pragma clang diagnostic ignored "-Wmodules-import-nested-redundant"
#endif


// 定义特性测试宏
#define _GNU_SOURCE
#define _DARWIN_C_SOURCE

// 基础类型定义 - 直接定义避免头文件冲突
typedef unsigned char   uint8_t;
typedef unsigned short  uint16_t;
typedef unsigned int    uint32_t;
typedef unsigned long long uint64_t;
typedef signed char     int8_t;
typedef short           int16_t;
typedef int             int32_t;
typedef long long       int64_t;
typedef unsigned long   size_t;
typedef long            ssize_t;
typedef int             socklen_t;
typedef uint32_t        sockaddr_t;

#ifndef _STDBOOL_H
typedef int bool;
#define true 1
#define false 0
#endif

// 系统调用声明 - 避免包含冲突的头文件
extern int socket(int, int, int);
extern int ioctl(int, unsigned long, ...);
extern int connect(int, const struct sockaddr *, socklen_t);
extern int getsockopt(int, int, int, void *, socklen_t *);
extern int close(int);
extern char *strerror(int);
extern int printf(const char *, ...);
extern void *memset(void *, int, size_t);
extern char *strncpy(char *, const char *, size_t);
extern char *strcpy(char *, const char *);
extern size_t strlen(const char *);
extern int strcmp(const char *, const char *);
extern int strncmp(const char *, const char *, size_t);
extern int atoi(const char *);
extern int getuid(void);
extern int getgid(void);
extern void pause(void);
extern unsigned int sleep(unsigned int);
extern void exit(int);
extern void (*signal(int, void (*)(int)))(int);

// 直接声明 tun2proxy 函数,防止SDK冲突
typedef enum Tun2proxyDns
{
    Tun2proxyDns_Virtual = 0,
    Tun2proxyDns_OverTcp,
    Tun2proxyDns_Direct,
} Tun2proxyDns;

typedef enum Tun2proxyVerbosity
{
    Tun2proxyVerbosity_Off = 0,
    Tun2proxyVerbosity_Error,
    Tun2proxyVerbosity_Warn,
    Tun2proxyVerbosity_Info,
    Tun2proxyVerbosity_Debug,
    Tun2proxyVerbosity_Trace,
} Tun2proxyVerbosity;

typedef struct Tun2proxyTrafficStatus
{
    uint64_t tx;
    uint64_t rx;
} Tun2proxyTrafficStatus;

extern void tun2proxy_set_log_callback(void (*callback)(enum Tun2proxyVerbosity, const char *, void *),
                                       void *ctx);
extern int tun2proxy_with_name_run(const char *proxy_url,
                                   const char *tun,
                                   const char *bypass,
                                   enum Tun2proxyDns dns_strategy,
                                   bool _root_privilege,
                                   enum Tun2proxyVerbosity verbosity);
extern int tun2proxy_with_fd_run(const char *proxy_url,
                                 int tun_fd,
                                 bool close_fd_on_drop,
                                 bool packet_information,
                                 unsigned short tun_mtu,
                                 enum Tun2proxyDns dns_strategy,
                                 enum Tun2proxyVerbosity verbosity);

extern int tun2proxy_run_with_cli_args(const char *cli_args,
                                       unsigned short tun_mtu,
                                       bool packet_information);

extern int tun2proxy_stop(void);

extern void tun2proxy_set_traffic_status_callback(uint32_t send_interval_secs,
                                                  void (*callback)(const struct Tun2proxyTrafficStatus *,
                                                                   void *),
                                                  void *ctx);

// 网络结构定义
struct sockaddr {
    uint8_t     sa_len;
    uint8_t     sa_family;
    char        sa_data[14];
};

struct ctl_info {
    uint32_t    ctl_id;
    char        ctl_name[96];
};

struct sockaddr_ctl {
    uint8_t     sc_len;
    uint8_t     sc_family;
    uint16_t    ss_sysaddr;
    uint32_t    sc_id;
    uint32_t    sc_unit;
    uint32_t    sc_reserved[5];
};

// 信号处理相关
struct sigaction {
    void (*sa_handler)(int);
    void (*sa_sigaction)(int, void *, void *);
    uint32_t sa_mask;
    int sa_flags;
};

// 错误号定义
extern int errno;

// 常量定义 - 避免头文件冲突
#define PF_SYSTEM           32
#define AF_SYSTEM           32
#define SYSPROTO_CONTROL    2
#define AF_SYS_CONTROL      2
#define SOCK_DGRAM          2
#define UTUN_OPT_IFNAME     2
#define UTUN_CONTROL_NAME   "com.apple.net.utun_control"
#define MAX_KCTL_NAME       96
#define CTLIOCGINFO         0xc0644e03U
#define O_RDWR              2
#define F_OK                0
#define SIGINT              2
#define SIGTERM             15

// NULL 定义
#ifndef NULL
#define NULL ((void*)0)
#endif

// 重复定义已删除，使用前面的定义

// iOS平台的utun相关常量定义
#ifndef SYSPROTO_CONTROL
#define SYSPROTO_CONTROL 2
#endif

#ifndef AF_SYS_CONTROL  
#define AF_SYS_CONTROL 2
#endif

#ifndef UTUN_OPT_IFNAME
#define UTUN_OPT_IFNAME 2
#endif

#ifndef UTUN_CONTROL_NAME
#define UTUN_CONTROL_NAME "com.apple.net.utun_control"
#endif

// 全局变量控制程序退出
static volatile bool g_running = true;

void signal_handler(int sig) {
    printf("\n收到信号 %d，准备退出...\n", sig);
    g_running = false;
}

// 日志回调
void log_callback(enum Tun2proxyVerbosity level, const char* msg, void* ctx) {
    const char* level_str = "";
    switch(level) {
        case Tun2proxyVerbosity_Off: level_str = "OFF"; break;
        case Tun2proxyVerbosity_Error: level_str = "ERROR"; break;
        case Tun2proxyVerbosity_Warn: level_str = "WARN"; break;
        case Tun2proxyVerbosity_Info: level_str = "INFO"; break;
        case Tun2proxyVerbosity_Debug: level_str = "DEBUG"; break;
        case Tun2proxyVerbosity_Trace: level_str = "TRACE"; break;
    }
    printf("[tun2proxy][%s] %s\n", level_str, msg);
}

// 流量统计回调
void traffic_callback(const struct Tun2proxyTrafficStatus* status, void* ctx) {
    printf("[traffic] TX: %llu bytes, RX: %llu bytes\n", status->tx, status->rx);
}

// 打开 utun 设备（标准方法）
int utun_open(int unit) {
    struct ctl_info info;
    struct sockaddr_ctl sc;
    int fd;

    memset(&info, 0, sizeof(info));
    strncpy(info.ctl_name, UTUN_CONTROL_NAME, MAX_KCTL_NAME);

    fd = socket(PF_SYSTEM, SOCK_DGRAM, SYSPROTO_CONTROL);
    if (fd < 0) {
        return -1;
    }

    if (ioctl(fd, CTLIOCGINFO, &info) < 0) {
        close(fd);
        return -1;
    }

    memset(&sc, 0, sizeof(sc));
    sc.sc_len    = sizeof(sc);
    sc.sc_family = AF_SYSTEM;
    sc.ss_sysaddr= AF_SYS_CONTROL;
    sc.sc_id     = info.ctl_id;
    sc.sc_unit   = unit; // 0 表示自动分配

    if (connect(fd, (struct sockaddr *)&sc, sizeof(sc)) < 0) {
        close(fd);
        return -1;
    }

    return fd;
}

// 显示帮助信息
void show_help(const char* program_name) {
    printf("=== iOS utun + tun2proxy 代理转发工具 ===\n");
    printf("用法: %s <代理地址> [选项]\n\n", program_name);
    printf("参数:\n");
    printf("  <代理地址>     SOCKS5代理地址，格式: socks5://host:port\n");
    printf("                 例如: socks5://127.0.0.1:1080\n\n");
    printf("选项:\n");
    printf("  -h, --help     显示此帮助信息\n");
    printf("  -v, --verbose  启用详细日志输出\n");
    printf("  -m, --mtu NUM  设置MTU大小 (默认: 1500)\n");
    printf("  -d, --dns MODE DNS处理模式:\n");
    printf("                 tcp    - 通过TCP发送DNS查询 (默认)\n");
    printf("                 direct - 直接处理，不拦截DNS\n");
    printf("                 virtual- 使用虚拟DNS服务器\n\n");
    printf("示例:\n");
    printf("  %s socks5://127.0.0.1:1080\n", program_name);
    printf("  %s socks5://*************:1080 -v -m 1400\n", program_name);
    printf("  %s socks5://proxy.example.com:1080 --dns direct\n", program_name);
}

int main(int argc, char *argv[]) {
    // 默认参数
    const char* proxy_url = NULL;
    int mtu = 1500;
    Tun2proxyDns dns_mode = Tun2proxyDns_OverTcp;
    Tun2proxyVerbosity verbose_level = Tun2proxyVerbosity_Info;

    // 解析命令行参数
    for (int i = 1; i < argc; i++) {
        if (strcmp(argv[i], "-h") == 0 || strcmp(argv[i], "--help") == 0) {
            show_help(argv[0]);
            return 0;
        } else if (strcmp(argv[i], "-v") == 0 || strcmp(argv[i], "--verbose") == 0) {
            verbose_level = Tun2proxyVerbosity_Debug;
        } else if (strcmp(argv[i], "-m") == 0 || strcmp(argv[i], "--mtu") == 0) {
            if (i + 1 >= argc) {
                printf("错误：-m/--mtu 选项需要指定MTU值\n");
                return -1;
            }
            mtu = atoi(argv[++i]);
            if (mtu < 500 || mtu > 9000) {
                printf("错误：MTU值应在 500-9000 范围内\n");
                return -1;
            }
        } else if (strcmp(argv[i], "-d") == 0 || strcmp(argv[i], "--dns") == 0) {
            if (i + 1 >= argc) {
                printf("错误：-d/--dns 选项需要指定DNS模式\n");
                return -1;
            }
            i++;
            if (strcmp(argv[i], "tcp") == 0) {
                dns_mode = Tun2proxyDns_OverTcp;
            } else if (strcmp(argv[i], "direct") == 0) {
                dns_mode = Tun2proxyDns_Direct;
            } else if (strcmp(argv[i], "virtual") == 0) {
                dns_mode = Tun2proxyDns_Virtual;
            } else {
                printf("错误：不支持的DNS模式 '%s'\n", argv[i]);
                printf("支持的模式: tcp, direct, virtual\n");
                return -1;
            }
        } else if (argv[i][0] == '-') {
            printf("错误：未知选项 '%s'\n", argv[i]);
            printf("使用 -h 或 --help 查看帮助信息\n");
            return -1;
        } else if (proxy_url == NULL) {
            // 第一个非选项参数作为代理地址
            proxy_url = argv[i];
        } else {
            printf("错误：太多参数 '%s'\n", argv[i]);
            printf("使用 -h 或 --help 查看帮助信息\n");
            return -1;
        }
    }

    // 检查必需参数
    if (proxy_url == NULL) {
        printf("错误：缺少代理地址参数\n");
        printf("使用 -h 或 --help 查看帮助信息\n");
        return -1;
    }

    // 验证代理地址格式
    if (strncmp(proxy_url, "socks5://", 9) != 0) {
        printf("错误：代理地址必须以 'socks5://' 开头\n");
        printf("示例: socks5://127.0.0.1:1080\n");
        return -1;
    }

    printf("=== iOS utun + tun2proxy 代理转发工具 ===\n");
    printf("代理地址: %s\n", proxy_url);
    printf("MTU: %d\n", mtu);
    printf("DNS模式: %s\n", 
           dns_mode == Tun2proxyDns_OverTcp ? "TCP" :
           dns_mode == Tun2proxyDns_Direct ? "Direct" : "Virtual");
    printf("日志级别: %s\n", 
           verbose_level == Tun2proxyVerbosity_Debug ? "Debug" : "Info");

    // 设置信号处理，优雅退出
    signal(SIGINT, signal_handler);
    signal(SIGTERM, signal_handler);

    int utun_fd = -1;
    int unit = 0;

    // 尝试打开 utun 设备，unit=0表示自动分配
    utun_fd = utun_open(unit);
    if (utun_fd < 0) {
        printf("错误：无法打开 utun 设备\n");
        return -1;
    }

    // 获取接口名
    char ifname[32] = {0};
    socklen_t len = sizeof(ifname);
    if (getsockopt(utun_fd, SYSPROTO_CONTROL, UTUN_OPT_IFNAME, ifname, &len) < 0) {
        printf("警告：无法获取接口名称，错误: %s\n", strerror(errno));
        // 继续运行
        strcpy(ifname, "utunX");
    }

    printf("✅ utun 设备打开成功: fd=%d, 接口名=%s\n", utun_fd, ifname);
    printf("请手动执行以下命令配置网络接口:\n");
    printf("  ifconfig %s 10.0.0.1/24 up\n", ifname);
    printf("  route add -net 10.0.0.0/24 -interface %s\n", ifname);

    // 设置 tun2proxy 日志回调
    tun2proxy_set_log_callback(log_callback, NULL);

    // 设置流量统计回调，每5秒回调一次
    tun2proxy_set_traffic_status_callback(5, traffic_callback, NULL);

    // 启动 tun2proxy，使用命令行参数
    int ret = tun2proxy_with_fd_run(
        proxy_url,          // 代理地址
        utun_fd,
        true,               // 关闭 fd 时关闭
        true,               // iOS utun 需要包信息头
        mtu,                // MTU
        dns_mode,           // DNS策略
        verbose_level       // 日志级别
    );

    if (ret != 0) {
        printf("错误：tun2proxy 启动失败，错误码 %d\n", ret);
        close(utun_fd);
        return -1;
    }

    printf("tun2proxy 已启动，开始转发流量...\n");

    // 阻塞等待退出信号
    while (g_running) {
        sleep(1);
    }

    printf("停止 tun2proxy...\n");
    tun2proxy_stop();

    close(utun_fd);

    printf("程序退出\n");
    return 0;
}
